#!/usr/bin/env python3
"""
关键词API扩充脚本
使用Google Keywords Planner API对去重后的关键词进行扩充
"""

import pandas as pd
import sys
import time
from pathlib import Path
from datetime import datetime
from typing import List, Dict

# 导入项目模块
sys.path.insert(0, '.')
from modules.api_expansion import GoogleKeywordsAPIClient
from modules.api_limiter import api_limiter
from modules.unified_storage import save_to_unified_storage

def load_deduplicated_keywords(file_path: str) -> List[str]:
    """
    从去重后的CSV文件加载关键词列表
    
    Args:
        file_path: CSV文件路径
        
    Returns:
        List[str]: 关键词列表
    """
    try:
        df = pd.read_csv(file_path, encoding='utf-8')
        keywords = df['keyword'].dropna().unique().tolist()
        
        print(f"✓ 从 {file_path} 加载了 {len(keywords)} 个唯一关键词")
        return keywords
        
    except Exception as e:
        print(f"❌ 加载关键词失败: {e}")
        raise

def expand_keywords_batch(keywords: List[str], batch_size: int = 50) -> List[Dict]:
    """
    批量扩充关键词
    
    Args:
        keywords: 关键词列表
        batch_size: 批处理大小
        
    Returns:
        List[Dict]: 扩充结果列表
    """
    print(f"开始批量扩充 {len(keywords)} 个关键词...")
    print(f"批处理大小: {batch_size}")
    
    # 初始化API客户端
    try:
        api_client = GoogleKeywordsAPIClient()
        print("✓ Google Keywords Planner API客户端初始化成功")
    except Exception as e:
        print(f"❌ API客户端初始化失败: {e}")
        raise
    
    all_expanded_keywords = []
    processed_count = 0
    skipped_count = 0
    
    # 检查初始API状态
    status = api_limiter.check_quota_status()
    print(f"\n初始API状态:")
    print(f"  当前API: {status.current_api_key_id}")
    print(f"  可用配额: {status.remaining_quota}/{api_limiter.max_calls_per_hour}")
    
    # 分批处理关键词
    for i in range(0, len(keywords), batch_size):
        batch = keywords[i:i+batch_size]
        batch_num = (i // batch_size) + 1
        total_batches = (len(keywords) + batch_size - 1) // batch_size
        
        print(f"\n处理批次 {batch_num}/{total_batches} ({len(batch)} 个关键词)")
        
        for j, keyword in enumerate(batch):
            # 检查API配额
            if not api_limiter.can_make_api_call():
                status = api_limiter.check_quota_status()
                print(f"\n⚠️ API配额不足，当前状态:")
                print(f"  API: {status.current_api_key_id}")
                print(f"  已用: {status.used_quota}/{api_limiter.max_calls_per_hour}")
                
                if status.next_available_time:
                    print(f"  下次可用: {status.next_available_time}")
                
                print(f"跳过剩余 {len(keywords) - processed_count - skipped_count} 个关键词")
                break
            
            try:
                print(f"  [{j+1}/{len(batch)}] 扩充关键词: {keyword}")
                
                # 调用API扩充
                expanded_keywords = api_client.expand_single_seed_word(keyword)
                
                if expanded_keywords:
                    all_expanded_keywords.extend(expanded_keywords)
                    print(f"    ✓ 获得 {len(expanded_keywords)} 个扩充关键词")
                else:
                    print(f"    ⚠️ 未获得扩充结果")
                
                processed_count += 1
                
                # 添加延迟避免过快调用
                time.sleep(1)
                
            except Exception as e:
                print(f"    ❌ 扩充失败: {e}")
                skipped_count += 1
                continue
        
        # 如果API配额用完，停止处理
        if not api_limiter.can_make_api_call():
            break
        
        # 批次间稍作停顿
        if batch_num < total_batches:
            print(f"批次 {batch_num} 完成，等待2秒...")
            time.sleep(2)
    
    print(f"\n扩充完成:")
    print(f"  处理成功: {processed_count} 个关键词")
    print(f"  跳过失败: {skipped_count} 个关键词")
    print(f"  总计获得: {len(all_expanded_keywords)} 个扩充关键词")
    
    return all_expanded_keywords

def save_expanded_results(original_keywords: List[str], expanded_keywords: List[Dict], 
                         output_file: str = None) -> str:
    """
    保存扩充结果
    
    Args:
        original_keywords: 原始关键词列表
        expanded_keywords: 扩充关键词列表
        output_file: 输出文件路径
        
    Returns:
        str: 输出文件路径
    """
    if output_file is None:
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        output_file = f"results/keyword_data_expanded_{timestamp}.csv"
    
    # 准备数据
    all_data = []
    
    # 添加原始关键词
    for keyword in original_keywords:
        all_data.append({
            'keyword': keyword,
            'type': 'original',
            'source': 'decomposition',
            'seed_word': keyword,
            'search_volume': None,
            'competition': None,
            'cpc': None,
            'timestamp': datetime.now().isoformat()
        })
    
    # 添加扩充关键词
    for item in expanded_keywords:
        all_data.append({
            'keyword': item.keyword,
            'type': 'expanded',
            'source': 'google_keyword_planner',
            'seed_word': item.seed_word,
            'search_volume': item.search_volume,
            'competition': item.competition,
            'cpc': item.cpc,
            'timestamp': item.timestamp
        })
    
    # 创建DataFrame并保存
    df = pd.DataFrame(all_data)
    
    # 去重（基于keyword列）
    df_dedup = df.drop_duplicates(subset=['keyword'], keep='first')
    
    # 按关键词排序
    df_dedup = df_dedup.sort_values('keyword').reset_index(drop=True)
    
    # 保存文件
    df_dedup.to_csv(output_file, index=False, encoding='utf-8')
    
    print(f"\n✓ 扩充结果已保存: {output_file}")
    print(f"  原始关键词: {len(original_keywords)}")
    print(f"  扩充关键词: {len(expanded_keywords)}")
    print(f"  去重后总计: {len(df_dedup)}")
    
    return output_file

def generate_expansion_report(expanded_keywords: List[Dict]) -> Dict:
    """
    生成扩充报告
    
    Args:
        expanded_keywords: 扩充关键词列表
        
    Returns:
        Dict: 扩充报告
    """
    if not expanded_keywords:
        return {"error": "没有扩充数据"}
    
    # 统计信息
    total_keywords = len(expanded_keywords)
    unique_seed_words = len(set(item.seed_word for item in expanded_keywords))
    
    # 搜索量统计
    search_volumes = [item.search_volume for item in expanded_keywords if item.search_volume is not None]
    avg_search_volume = sum(search_volumes) / len(search_volumes) if search_volumes else 0
    
    # 竞争度统计
    competitions = [item.competition for item in expanded_keywords if item.competition is not None]
    competition_counts = {}
    for comp in competitions:
        competition_counts[comp] = competition_counts.get(comp, 0) + 1
    
    # CPC统计
    cpcs = [item.cpc for item in expanded_keywords if item.cpc is not None]
    avg_cpc = sum(cpcs) / len(cpcs) if cpcs else 0
    
    # 按种子词分组统计
    seed_word_stats = {}
    for item in expanded_keywords:
        seed = item.seed_word
        if seed not in seed_word_stats:
            seed_word_stats[seed] = 0
        seed_word_stats[seed] += 1
    
    # 排序获取扩充最多的种子词
    top_seed_words = sorted(seed_word_stats.items(), key=lambda x: x[1], reverse=True)[:10]
    
    report = {
        "summary": {
            "total_expanded_keywords": total_keywords,
            "unique_seed_words": unique_seed_words,
            "avg_keywords_per_seed": total_keywords / unique_seed_words if unique_seed_words > 0 else 0
        },
        "search_volume": {
            "keywords_with_volume": len(search_volumes),
            "average_volume": avg_search_volume,
            "total_volume": sum(search_volumes)
        },
        "competition": competition_counts,
        "cpc": {
            "keywords_with_cpc": len(cpcs),
            "average_cpc": avg_cpc
        },
        "top_seed_words": top_seed_words
    }
    
    return report

def main():
    """主函数"""
    print("Google Keywords Planner API 关键词扩充")
    print("="*60)
    
    # 输入文件路径
    input_file = "results/keyword_data_dedup_first_20250717_132241.csv"
    
    # 检查文件是否存在
    if not Path(input_file).exists():
        print(f"❌ 输入文件不存在: {input_file}")
        return 1
    
    try:
        # 1. 加载关键词
        keywords = load_deduplicated_keywords(input_file)
        
        if not keywords:
            print("❌ 没有找到关键词")
            return 1
        
        # 限制关键词数量以控制API调用
        max_keywords = 100  # 可以根据需要调整
        if len(keywords) > max_keywords:
            print(f"⚠️ 关键词数量过多 ({len(keywords)})，限制为前 {max_keywords} 个")
            keywords = keywords[:max_keywords]
        
        # 2. 检查API状态
        status = api_limiter.check_quota_status()
        if not status.can_make_call:
            print(f"❌ API配额不足，无法进行扩充")
            print(f"下次可用时间: {status.next_available_time}")
            return 1
        
        # 3. 批量扩充关键词
        expanded_keywords = expand_keywords_batch(keywords, batch_size=20)
        
        if not expanded_keywords:
            print("❌ 没有获得任何扩充结果")
            return 1
        
        # 4. 保存结果
        output_file = save_expanded_results(keywords, expanded_keywords)
        
        # 5. 生成报告
        report = generate_expansion_report(expanded_keywords)
        report_file = save_to_unified_storage(report, "json", "expansion_report.json")
        
        # 6. 显示摘要
        print(f"\n🎉 扩充完成!")
        print(f"输出文件: {output_file}")
        print(f"报告文件: {report_file}")
        
        print(f"\n📊 扩充摘要:")
        print(f"  原始关键词: {len(keywords)}")
        print(f"  扩充关键词: {len(expanded_keywords)}")
        print(f"  平均扩充率: {len(expanded_keywords)/len(keywords):.1f}x")
        
        if 'search_volume' in report:
            print(f"  平均搜索量: {report['search_volume']['average_volume']:.0f}")
        
        # 7. 生成API使用报告
        api_report = api_limiter.generate_usage_report()
        save_to_unified_storage(api_report, "json", "api_usage_after_expansion.json")
        
        return 0
        
    except Exception as e:
        print(f"\n❌ 扩充过程失败: {e}")
        import traceback
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    sys.exit(main())
