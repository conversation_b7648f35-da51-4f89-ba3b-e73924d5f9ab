#!/usr/bin/env python3
"""
关键词去重脚本
根据keyword列对keyword_data.csv进行去重处理
"""

import pandas as pd
import sys
from pathlib import Path
from datetime import datetime

def deduplicate_keywords(input_file="results/keyword_data.csv", output_file=None, strategy="first"):
    """
    对关键词CSV文件进行去重
    
    Args:
        input_file: 输入文件路径
        output_file: 输出文件路径，如果为None则自动生成
        strategy: 去重策略 ("first", "last", "highest_confidence", "most_recent")
    
    Returns:
        str: 输出文件路径
    """
    
    print(f"开始处理文件: {input_file}")
    
    # 检查输入文件是否存在
    if not Path(input_file).exists():
        raise FileNotFoundError(f"输入文件不存在: {input_file}")
    
    # 读取CSV文件
    try:
        df = pd.read_csv(input_file, encoding='utf-8')
        print(f"✓ 成功读取文件，共 {len(df)} 行数据")
    except Exception as e:
        raise Exception(f"读取文件失败: {e}")
    
    # 显示去重前的统计信息
    print(f"\n去重前统计:")
    print(f"  总行数: {len(df)}")
    print(f"  唯一关键词数: {df['keyword'].nunique()}")
    print(f"  重复关键词数: {len(df) - df['keyword'].nunique()}")
    
    # 显示重复最多的关键词
    duplicate_counts = df['keyword'].value_counts()
    top_duplicates = duplicate_counts[duplicate_counts > 1].head(10)
    if len(top_duplicates) > 0:
        print(f"\n重复最多的关键词:")
        for keyword, count in top_duplicates.items():
            print(f"  '{keyword}': {count} 次")
    
    # 根据策略进行去重
    print(f"\n使用去重策略: {strategy}")
    
    if strategy == "first":
        # 保留第一次出现的记录
        df_dedup = df.drop_duplicates(subset=['keyword'], keep='first')
        
    elif strategy == "last":
        # 保留最后一次出现的记录
        df_dedup = df.drop_duplicates(subset=['keyword'], keep='last')
        
    elif strategy == "highest_confidence":
        # 保留置信度最高的记录
        df_dedup = df.loc[df.groupby('keyword')['confidence'].idxmax()]
        
    elif strategy == "most_recent":
        # 保留时间戳最新的记录
        df['timestamp'] = pd.to_datetime(df['timestamp'])
        df_dedup = df.loc[df.groupby('keyword')['timestamp'].idxmax()]
        
    else:
        raise ValueError(f"不支持的去重策略: {strategy}")
    
    # 按关键词字母顺序排序
    df_dedup = df_dedup.sort_values('keyword').reset_index(drop=True)
    
    # 生成输出文件名
    if output_file is None:
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        output_file = f"results/keyword_data_dedup_{strategy}_{timestamp}.csv"
    
    # 保存去重后的文件
    try:
        df_dedup.to_csv(output_file, index=False, encoding='utf-8')
        print(f"✓ 去重后文件已保存: {output_file}")
    except Exception as e:
        raise Exception(f"保存文件失败: {e}")
    
    # 显示去重后的统计信息
    print(f"\n去重后统计:")
    print(f"  总行数: {len(df_dedup)}")
    print(f"  唯一关键词数: {df_dedup['keyword'].nunique()}")
    print(f"  删除重复行数: {len(df) - len(df_dedup)}")
    print(f"  去重率: {((len(df) - len(df_dedup)) / len(df) * 100):.1f}%")
    
    # 显示一些示例关键词
    print(f"\n示例关键词 (前10个):")
    for i, row in df_dedup.head(10).iterrows():
        print(f"  {i+1}. {row['keyword']} (置信度: {row['confidence']:.3f})")
    
    return output_file

def main():
    """主函数"""
    print("关键词去重脚本")
    print("="*50)
    
    # 可选的去重策略
    strategies = {
        "1": ("first", "保留第一次出现的记录"),
        "2": ("last", "保留最后一次出现的记录"), 
        "3": ("highest_confidence", "保留置信度最高的记录"),
        "4": ("most_recent", "保留时间戳最新的记录")
    }
    
    # 如果有命令行参数，直接使用
    if len(sys.argv) > 1:
        strategy_key = sys.argv[1]
        if strategy_key in strategies:
            strategy = strategies[strategy_key][0]
        else:
            strategy = strategy_key  # 直接使用策略名称
    else:
        # 交互式选择策略
        print("\n请选择去重策略:")
        for key, (strategy, desc) in strategies.items():
            print(f"  {key}. {desc}")
        
        choice = input("\n请输入选择 (1-4, 默认为1): ").strip()
        if not choice:
            choice = "1"
        
        if choice not in strategies:
            print("无效选择，使用默认策略")
            choice = "1"
        
        strategy = strategies[choice][0]
    
    try:
        # 执行去重
        output_file = deduplicate_keywords(strategy=strategy)
        
        print(f"\n🎉 去重完成!")
        print(f"输出文件: {output_file}")
        
        # 询问是否替换原文件
        replace = input("\n是否用去重后的文件替换原文件? (y/N): ").strip().lower()
        if replace in ['y', 'yes']:
            import shutil
            shutil.copy2(output_file, "results/keyword_data.csv")
            print("✓ 原文件已替换")
        
    except Exception as e:
        print(f"\n❌ 处理失败: {e}")
        return 1
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
