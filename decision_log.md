# Keyword Architect Agent - Decision Log
# 关键词架构师代理 - 架构决策记录
# Version: 1.0
# Date: 2025-07-17

## ADR-001: 选择Python作为主要开发语言

**日期**: 2025-07-17  
**状态**: 已接受  
**决策者**: 开发团队  

### 背景
需要选择合适的编程语言来实现关键词架构师代理系统。

### 决策
选择Python 3.9+作为主要开发语言。

### 理由
1. **生态系统成熟**: 丰富的数据处理和AI库支持
2. **API集成便利**: 优秀的HTTP客户端和API集成库
3. **AutoGen支持**: AutoGen v0.4对Python支持最完善
4. **数据处理优势**: pandas等库对CSV处理和数据分析支持良好
5. **团队熟悉度**: 开发团队对Python较为熟悉

### 后果
- 正面: 开发效率高，库支持丰富
- 负面: 运行时性能相对较低（但对当前需求足够）

---

## ADR-002: 使用AutoGen v0.4框架处理LLM交互

**日期**: 2025-07-17  
**状态**: 已接受  
**决策者**: 开发团队  

### 背景
系统需要与Google Gemini 2.5 Flash进行大量交互，需要选择合适的LLM框架。

### 决策
使用AutoGen v0.4框架处理所有LLM相关功能。

### 理由
1. **编码规则要求**: knowledge/coding_extra_rules.md明确要求使用AutoGen v0.4
2. **避免重复造轮子**: 符合项目编码原则
3. **功能完整**: 提供完整的LLM交互和Agent功能
4. **文档支持**: 有完整的参考文档在knowledge/autogen_framework_reference

### 后果
- 正面: 减少开发工作量，功能稳定可靠
- 负面: 增加了框架依赖，需要学习框架API

---

## ADR-003: 采用prompt_manager系统管理提示词

**日期**: 2025-07-17  
**状态**: 已接受  
**决策者**: 开发团队  

### 背景
系统需要管理多个复杂的提示词模板，需要统一的管理方案。

### 决策
使用现有的prompt_manager系统管理所有提示词。

### 理由
1. **编码规则要求**: coding_extra_rules.md要求使用prompt_manager系统
2. **现有资源**: 项目中已有prompt_manager源代码和文档
3. **统一管理**: 避免提示词散落在代码中
4. **版本控制**: 便于提示词的版本管理和优化

### 后果
- 正面: 提示词管理规范，易于维护和优化
- 负面: 需要学习prompt_manager的使用方法

---

## ADR-004: 实现严格的API调用限制保护机制

**日期**: 2025-07-17  
**状态**: 已接受  
**决策者**: 开发团队  

### 背景
Google Keywords Planner API调用次数非常有限，需要严格控制使用。

### 决策
实现多层次的API调用保护机制：
1. 调用前检查历史记录
2. 24小时缓存机制
3. 调用频率限制（10次/小时）
4. 详细的调用日志记录

### 理由
1. **成本控制**: API调用次数限制严格
2. **系统稳定性**: 避免因API限制导致系统不可用
3. **数据复用**: 缓存机制提高效率
4. **可追溯性**: 详细记录便于问题排查

### 后果
- 正面: 保护API配额，提高系统稳定性
- 负面: 增加了系统复杂度，需要额外的缓存管理

---

## ADR-005: 采用函数式架构设计

**日期**: 2025-07-17  
**状态**: 已接受  
**决策者**: 开发团队  

### 背景
项目要求"每个功能实现为一个函数"，需要确定具体的架构模式。

### 决策
采用函数式架构设计，每个核心功能实现为独立函数：
- `decompose_product_names()`
- `expand_keywords_via_api()`
- `associate_keywords_via_llm()`
- `consolidate_results()`

### 理由
1. **项目要求**: 明确要求功能函数化
2. **简化设计**: 避免过度的面向对象设计
3. **易于测试**: 函数式设计便于单元测试
4. **符合原则**: 遵循"禁止封装过度"的编码规则

### 后果
- 正面: 代码简洁，易于理解和测试
- 负面: 可能需要在函数间传递较多参数

---

## ADR-006: 使用CSV和JSON混合存储策略

**日期**: 2025-07-17  
**状态**: 已接受  
**决策者**: 开发团队  

### 背景
需要选择合适的数据存储格式来保存处理结果。

### 决策
采用混合存储策略：
- 最终结果: CSV格式（便于Excel分析）
- 中间结果: JSON格式（保持数据结构）
- 日志记录: JSON格式（结构化日志）

### 理由
1. **用户友好**: CSV格式便于营销人员使用Excel分析
2. **数据完整性**: JSON保持复杂数据结构
3. **系统简化**: 避免引入数据库的复杂性
4. **符合要求**: 满足"统一存储，易于寻找"的要求

### 后果
- 正面: 存储简单，用户友好
- 负面: 大数据量时性能可能受限

---

## ADR-007: 实现三阶段处理流水线

**日期**: 2025-07-17  
**状态**: 已接受  
**决策者**: 开发团队  

### 背景
系统需要实现原子化拆解、API扩展、LLM联想三个核心功能。

### 决策
实现线性的三阶段处理流水线：
1. 阶段1: 原子化拆解（产品名 → 种子词）
2. 阶段2: API扩展（种子词 → 关键词列表）
3. 阶段3: LLM联想（关键词 → 深层关联词）

### 理由
1. **逻辑清晰**: 符合自然的处理流程
2. **易于调试**: 每个阶段可以独立验证
3. **缓存友好**: 每个阶段的结果都可以缓存
4. **错误隔离**: 单个阶段的错误不会影响其他阶段

### 后果
- 正面: 架构清晰，易于维护
- 负面: 可能无法充分利用并行处理机会

---

## ADR-008: 采用配置文件驱动的参数管理

**日期**: 2025-07-17  
**状态**: 已接受  
**决策者**: 开发团队  

### 背景
系统有多个可配置参数，需要统一的配置管理方案。

### 决策
使用TOML格式的配置文件（deployment.config.toml）管理所有系统参数。

### 理由
1. **配置集中**: 所有参数统一管理
2. **格式友好**: TOML格式易读易写
3. **环境分离**: 支持不同环境的配置
4. **版本控制**: 配置变更可追踪

### 后果
- 正面: 配置管理规范，易于维护
- 负面: 需要额外的配置解析逻辑

---

## ADR-009: 实现渐进式错误处理策略

**日期**: 2025-07-17  
**状态**: 已接受  
**决策者**: 开发团队  

### 背景
系统依赖多个外部API，需要健壮的错误处理机制。

### 决策
实现渐进式错误处理策略：
1. 重试机制（最多3次）
2. 降级处理（使用缓存数据）
3. 部分结果返回
4. 详细错误记录

### 理由
1. **系统稳定性**: 避免单点故障导致系统崩溃
2. **用户体验**: 尽可能提供部分结果
3. **问题排查**: 详细记录便于问题定位
4. **业务连续性**: 缓存机制保证基本功能

### 后果
- 正面: 系统健壮性高，用户体验好
- 负面: 增加了错误处理的复杂度

---

## 决策变更记录

### 变更日志
- 2025-07-17: 初始版本，创建ADR-001至ADR-009

### 待决策事项
1. 是否需要实现并行处理机制
2. 是否需要添加Web界面
3. 是否需要实现数据库存储

### 已废弃决策
无
