# Keyword Architect Agent - 关键词架构师代理

## 项目概述
本项目是一个关键词研究和扩展工具，专门为生物医学产品（如ELISA试剂盒、过敏测试等）生成全面的关键词策略。

## 核心需求
1. 管理文件结构，除程序主入口外，主目录不要有太多文件
2. 无用的测试文件和被淘汰的文档文件需要被即时删除
3. 以下功能的实现方式为一个函数
4. 存储结果统一存放，易于寻找
5. 这个功能要求较为简单，需要简化系统实现
6. 生成的文档放置在docs目录下
7. 遵循 knowledge/

## 核心工作流程
### A. 原子化拆解 (Atomic Decomposition)
- **目标**: 使用Google Gemini 2.5 Flash模型将每个产品名（如Human IL-6 ELISA Kit）拆解为“种子词”（ELISA, IL-6 test等）
- **输入**: 产品以表的形式存储在source/backed.csv
- **输出**: 拆解结果要被保存

### B. API驱动扩展 (API-Driven Expansion)
- **目标**: 调用Google Keywords Planner的API，基于种子词获取海量相关关键词
- **参考**: google keyword planner的调取方式参考keyword_tools_dev.py
- **配置**: 相关设置在google-ads.yaml
- **重要**: google keyword planner的api调用次数非常有限，调用该api时务必要存储其调用记录

### C. LLM驱动联想 (LLM-Driven Association)
- **目标**: 利用Google Gemini 2.5 Flash LLM进行语义和场景联想
- **功能**: 获取API无法覆盖的、更深层次，以关键词形式出现的用户查询
- **输出**: 最后以表格的形式存储

## API配置
- **Google LLM API Key**: AIzaSyBWPiNDni24gEzdZUzNUhVxws6prDHB4Yo
- **Google Ads配置**: 见google-ads.yaml文件