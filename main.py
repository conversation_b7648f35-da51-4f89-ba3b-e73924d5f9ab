#!/usr/bin/env python3
"""
Keyword Architect Agent - 主入口
关键词架构师代理 - 生物医学产品关键词研究和扩展工具

严格遵循项目要求的简化实现:
- 使用AutoGen v0.4处理所有LLM功能
- 使用prompt_manager管理所有提示词
- 严格管理Google Keywords Planner API调用限制
- 统一存储所有结果到results/目录
"""

import sys
import argparse
import time
from datetime import datetime
from pathlib import Path
from typing import List, Dict, Any

# 导入核心模块
try:
    from modules.decomposition import load_csv_data, decompose_product_names
    from modules.api_expansion import expand_keywords_via_api, get_seed_words_from_decomposition
    from modules.llm_association import associate_keywords_via_llm, get_keywords_from_expansion
    from modules.unified_storage import storage_manager, ProcessingMetadata
    from modules.api_limiter import api_limiter
except ImportError as e:
    print(f"[ERROR] 模块导入失败: {e}")
    print("请确保所有依赖模块已正确安装")
    sys.exit(1)


def print_banner():
    """打印程序横幅"""
    banner = """
╔══════════════════════════════════════════════════════════════╗
║                    Keyword Architect Agent                   ║
║                   关键词架构师代理 v1.0                        ║
║                                                              ║
║  生物医学产品关键词研究和扩展工具                                ║
║  - AutoGen v0.4 + Google Gemini 2.5 Flash                  ║
║  - Google Keywords Planner API (严格限制管理)                ║
║  - 智能语义联想和场景扩展                                      ║
╚══════════════════════════════════════════════════════════════╝
"""
    print(banner)


def parse_arguments() -> argparse.Namespace:
    """
    解析命令行参数

    Returns:
        argparse.Namespace: 解析后的参数
    """
    parser = argparse.ArgumentParser(
        description="Keyword Architect Agent - 生物医学产品关键词研究工具",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        add_help=True
    )
    
    parser.add_argument(
        "--input", "-i",
        default="source/backed.csv",
        help="输入CSV文件路径 (默认: source/backed.csv)"
    )
    
    parser.add_argument(
        "--output", "-o", 
        default="results",
        help="输出目录路径 (默认: results)"
    )
    
    parser.add_argument(
        "--skip-decomposition",
        action="store_true",
        help="跳过原子化拆解阶段（使用现有结果）"
    )
    
    parser.add_argument(
        "--skip-api",
        action="store_true", 
        help="跳过API扩展阶段（使用现有结果或缓存）"
    )
    
    parser.add_argument(
        "--skip-association",
        action="store_true",
        help="跳过LLM联想阶段（使用现有结果）"
    )
    
    parser.add_argument(
        "--api-status",
        action="store_true",
        help="仅显示API配额状态"
    )
    
    return parser.parse_args()


def check_prerequisites() -> bool:
    """
    检查运行前提条件
    
    Returns:
        bool: 是否满足前提条件
    """
    print("[MAIN] 检查运行前提条件...")
    
    # 检查输入文件
    input_file = Path("source/backed.csv")
    if not input_file.exists():
        print(f"[ERROR] 输入文件不存在: {input_file}")
        return False
    
    # 检查Google Ads配置
    config_file = Path("google-ads.yaml")
    if not config_file.exists():
        print(f"[ERROR] Google Ads配置文件不存在: {config_file}")
        return False
    
    # 检查API密钥
    api_key = "AIzaSyBWPiNDni24gEzdZUzNUhVxws6prDHB4Yo"
    if not api_key:
        print("[ERROR] Google Gemini API密钥未配置")
        return False
    
    print("[MAIN] 前提条件检查通过")
    return True


def show_api_status():
    """显示API配额状态"""
    print("\n" + "="*60)
    print("API配额状态")
    print("="*60)
    
    # Google Keywords Planner API状态
    quota_status = api_limiter.check_quota_status()
    print(f"Google Keywords Planner API:")
    print(f"  当前小时已用: {quota_status.used_quota}/{api_limiter.max_calls_per_hour}")
    print(f"  剩余配额: {quota_status.remaining_quota}")
    print(f"  可以调用: {'是' if quota_status.can_make_call else '否'}")
    
    if quota_status.next_available_time:
        print(f"  下次可用时间: {quota_status.next_available_time}")
    
    # 使用报告
    usage_report = api_limiter.generate_usage_report()
    stats = usage_report['last_24h_stats']
    print(f"\n最近24小时统计:")
    print(f"  总调用次数: {stats['total_calls']}")
    print(f"  成功调用: {stats['successful_calls']}")
    print(f"  失败调用: {stats['failed_calls']}")
    print(f"  获得结果总数: {stats['total_results']}")
    print(f"  平均响应时间: {stats['average_response_time']:.2f}秒")


def run_full_pipeline(input_file: str, skip_decomposition: bool = False, 
                     skip_api: bool = False, skip_association: bool = False) -> bool:
    """
    运行完整的关键词处理流水线
    
    Args:
        input_file: 输入CSV文件路径
        skip_decomposition: 是否跳过拆解阶段
        skip_api: 是否跳过API扩展阶段
        skip_association: 是否跳过LLM联想阶段
        
    Returns:
        bool: 处理是否成功
    """
    start_time = datetime.now()
    
    try:
        print(f"\n[MAIN] 开始处理流水线...")
        print(f"[MAIN] 输入文件: {input_file}")
        print(f"[MAIN] 开始时间: {start_time.strftime('%Y-%m-%d %H:%M:%S')}")
        
        # 统计变量
        total_products = 0
        total_seed_words = 0
        total_expanded_keywords = 0
        total_associated_keywords = 0
        api_calls_made = 0
        
        # A工作流：原子化拆解
        if not skip_decomposition:
            print(f"\n{'='*60}")
            print("A工作流：原子化拆解 (AutoGen + Google Gemini 2.5 Flash)")
            print("="*60)
            
            # 加载产品数据
            products = load_csv_data(input_file)
            total_products = len(products)
            
            # 拆解产品名称
            seed_words = decompose_product_names(products)
            total_seed_words = len(seed_words)
            
        else:
            print(f"\n[MAIN] 跳过原子化拆解阶段")
            seed_words = []
        
        # B工作流：API驱动扩展
        if not skip_api:
            print(f"\n{'='*60}")
            print("B工作流：API驱动扩展 (Google Keywords Planner API)")
            print("="*60)
            
            # 获取种子词
            if not seed_words:
                seed_word_list = get_seed_words_from_decomposition()
            else:
                seed_word_list = [sw.keyword for sw in seed_words]
            
            # API扩展
            if seed_word_list:
                initial_calls = api_limiter.check_quota_status().used_quota
                expanded_keywords = expand_keywords_via_api(seed_word_list)
                final_calls = api_limiter.check_quota_status().used_quota
                api_calls_made = final_calls - initial_calls
                total_expanded_keywords = len(expanded_keywords)
            else:
                print("[WARNING] 没有种子词可用于API扩展")
                expanded_keywords = []
                
        else:
            print(f"\n[MAIN] 跳过API扩展阶段")
            expanded_keywords = []
        
        # C工作流：LLM驱动联想
        if not skip_association:
            print(f"\n{'='*60}")
            print("C工作流：LLM驱动联想 (AutoGen + Google Gemini)")
            print("="*60)
            
            # 获取关键词
            if not expanded_keywords:
                keyword_list = get_keywords_from_expansion()
            else:
                keyword_list = [ek.keyword for ek in expanded_keywords]
            
            # LLM联想
            if keyword_list:
                associated_keywords = associate_keywords_via_llm(keyword_list)
                total_associated_keywords = len(associated_keywords)
            else:
                print("[WARNING] 没有关键词可用于LLM联想")
                associated_keywords = []
                
        else:
            print(f"\n[MAIN] 跳过LLM联想阶段")
            associated_keywords = []
        
        # 统一存储和结果整合
        print(f"\n{'='*60}")
        print("结果整合和统一存储")
        print("="*60)
        
        # 准备数据
        decomposed_data = [sw.__dict__ if hasattr(sw, '__dict__') else sw for sw in seed_words] if seed_words else []
        expanded_data = [ek.__dict__ if hasattr(ek, '__dict__') else ek for ek in expanded_keywords] if expanded_keywords else []
        associated_data = [ak.__dict__ if hasattr(ak, '__dict__') else ak for ak in associated_keywords] if associated_keywords else []
        
        # 整合最终结果
        final_output = storage_manager.consolidate_and_save_final_results(
            decomposed_data, expanded_data, associated_data
        )
        
        # 生成处理元数据
        end_time = datetime.now()
        processing_duration = (end_time - start_time).total_seconds()
        
        metadata = ProcessingMetadata(
            start_time=start_time.isoformat(),
            end_time=end_time.isoformat(),
            total_products=total_products,
            total_seed_words=total_seed_words,
            total_expanded_keywords=total_expanded_keywords,
            total_associated_keywords=total_associated_keywords,
            api_calls_made=api_calls_made,
            processing_duration=processing_duration,
            success=True
        )
        
        storage_manager.save_processing_metadata(metadata)
        
        # 生成摘要报告
        storage_manager.generate_summary_report(final_output)
        
        # 导出多种格式
        storage_manager.export_to_multiple_formats(final_output)
        
        # 清理临时文件
        storage_manager.cleanup_temp_files()
        
        # 显示最终结果
        print(f"\n{'='*60}")
        print("处理完成")
        print("="*60)
        print(f"处理时间: {processing_duration:.2f} 秒")
        print(f"输入产品数: {total_products}")
        print(f"生成种子词: {total_seed_words}")
        print(f"扩展关键词: {total_expanded_keywords}")
        print(f"联想关键词: {total_associated_keywords}")
        print(f"API调用次数: {api_calls_made}")
        print(f"最终结果文件: {final_output}")
        
        return True
        
    except Exception as e:
        end_time = datetime.now()
        processing_duration = (end_time - start_time).total_seconds()
        
        print(f"\n[ERROR] 处理流水线失败: {e}")
        
        # 保存错误元数据
        error_metadata = ProcessingMetadata(
            start_time=start_time.isoformat(),
            end_time=end_time.isoformat(),
            total_products=total_products,
            total_seed_words=total_seed_words,
            total_expanded_keywords=total_expanded_keywords,
            total_associated_keywords=total_associated_keywords,
            api_calls_made=api_calls_made,
            processing_duration=processing_duration,
            success=False,
            error_message=str(e)
        )
        
        storage_manager.save_processing_metadata(error_metadata)
        
        return False


def main() -> int:
    """
    主函数
    
    Returns:
        int: 退出码 (0=成功, 1=失败)
    """
    # 打印横幅
    print_banner()
    
    # 解析参数
    args = parse_arguments()
    
    # 仅显示API状态
    if args.api_status:
        show_api_status()
        return 0
    
    # 检查前提条件
    if not check_prerequisites():
        return 1
    
    # 显示当前API状态
    show_api_status()
    
    # 运行处理流水线
    success = run_full_pipeline(
        input_file=args.input,
        skip_decomposition=args.skip_decomposition,
        skip_api=args.skip_api,
        skip_association=args.skip_association
    )
    
    return 0 if success else 1


if __name__ == "__main__":
    sys.exit(main())
