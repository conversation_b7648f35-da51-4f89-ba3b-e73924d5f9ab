# Keyword Architect Agent - Deployment Configuration
# 关键词架构师代理 - 部署配置文件
# Version: 1.0
# Date: 2025-07-17

[project]
name = "keyword_architect_agent"
version = "1.0.0"
description = "生物医学产品关键词研究和扩展工具"
author = "Keyword Architect Team"

[environment]
python_version = "3.9+"
required_packages = [
    "autogen[gemini]==0.4.*",
    "google-ads==24.*",
    "pandas>=1.5.0",
    "pyyaml>=6.0",
    "python-dotenv>=1.0.0",
    "requests>=2.28.0"
]

[runtime]
# 运行时配置
execution_mode = "local"  # local, docker, cloud
log_level = "INFO"
max_concurrent_requests = 3  # 限制并发API调用

[apis]
# API配置和限制
[apis.google_gemini]
model = "gemini-2.5-flash"
api_key_env = "GOOGLE_GEMINI_API_KEY"
default_api_key = "AIzaSyBWPiNDni24gEzdZUzNUhVxws6prDHB4Yo"
max_tokens = 8192
temperature = 0.7
timeout = 30

[apis.google_ads]
config_file = "google-ads.yaml"
api_version = "v14"
rate_limit = "10/hour"  # 严格限制调用频率
cache_duration = "24h"  # 缓存API结果24小时

[storage]
# 数据存储配置
[storage.input]
source_data = "source/backed.csv"
encoding = "utf-8"

[storage.output]
base_directory = "results"
keyword_data = "results/keyword_data.csv"
api_logs = "results/api_call_logs.json"
decomposition_results = "results/decomposition_results.json"
association_results = "results/association_results.json"

[storage.cache]
directory = "cache"
api_cache = "cache/api_responses.json"
decomposition_cache = "cache/decomposition_cache.json"

[processing]
# 处理配置
[processing.decomposition]
batch_size = 10  # 每批处理的产品数量
retry_attempts = 3
timeout = 60

[processing.api_expansion]
keywords_per_seed = 50  # 每个种子词获取的关键词数量
include_search_volume = true
include_competition = true

[processing.llm_association]
association_depth = 3  # 联想深度级别
scenarios = ["clinical_use", "research_application", "commercial_search"]
max_associations_per_keyword = 20

[security]
# 安全配置
api_key_rotation = false  # 是否启用API密钥轮换
request_logging = true    # 是否记录API请求
sensitive_data_masking = true  # 是否屏蔽敏感数据

[monitoring]
# 监控配置
enable_metrics = true
api_usage_tracking = true
performance_logging = true
error_reporting = true

[development]
# 开发环境配置
debug_mode = false
verbose_logging = false
test_data_size = 10  # 测试时处理的数据量

[production]
# 生产环境配置
optimize_performance = true
enable_caching = true
batch_processing = true
parallel_execution = false  # 避免API限制冲突

[file_management]
# 文件管理配置
auto_cleanup = true  # 自动清理临时文件
backup_results = true  # 备份重要结果
max_log_size = "100MB"
log_rotation = true

[compliance]
# 合规性配置
data_retention_days = 90
privacy_mode = true
audit_logging = true

[integration]
# 集成配置
[integration.autogen]
version = "0.4"
config_path = "configs/autogen_config.json"

[integration.prompt_manager]
source_path = "prompt_manager"
templates_path = "prompt_manager/templates"
