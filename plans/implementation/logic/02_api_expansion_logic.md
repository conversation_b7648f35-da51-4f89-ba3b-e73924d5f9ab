# API Expansion Logic - API驱动扩展逻辑
# Version: 1.0
# Date: 2025-07-17
# Phase: 1.8 Logic Design & Verification

## 1. 逻辑概述

### 1.1 功能目标
使用Google Keywords Planner API将种子关键词扩展为包含搜索量和竞争度数据的关键词列表，同时严格控制API调用次数。

### 1.2 核心约束
- **API限制**: 每小时最多10次调用
- **缓存优先**: 24小时缓存有效期
- **批量处理**: 合并相似请求提高效率
- **错误恢复**: 多层次降级策略

## 2. API调用限制管理逻辑

### 2.1 调用历史跟踪逻辑
```python
def track_api_call_history(api_call: APICallRecord) -> None:
    """
    跟踪和记录API调用历史
    
    逻辑步骤:
    1. 加载现有调用历史
    2. 添加新的调用记录
    3. 清理过期记录
    4. 持久化更新历史
    """
    
    # Step 1: 加载历史记录
    history = load_api_call_history()
    
    # Step 2: 添加新记录
    api_call.timestamp = datetime.now()
    history.append(api_call)
    
    # Step 3: 清理过期记录 (保留24小时内的记录)
    cutoff_time = datetime.now() - timedelta(hours=24)
    history = [call for call in history if call.timestamp > cutoff_time]
    
    # Step 4: 持久化
    save_api_call_history(history)
```

### 2.2 调用配额检查逻辑
```python
def check_api_quota_availability() -> QuotaStatus:
    """
    检查当前API调用配额状态
    
    逻辑步骤:
    1. 获取当前小时的调用记录
    2. 计算已使用配额
    3. 评估剩余配额
    4. 预测下次可用时间
    """
    
    # Step 1: 获取当前小时的调用
    current_hour = datetime.now().replace(minute=0, second=0, microsecond=0)
    history = load_api_call_history()
    current_hour_calls = [
        call for call in history 
        if call.timestamp >= current_hour and call.success
    ]
    
    # Step 2: 计算使用量
    used_quota = len(current_hour_calls)
    max_quota = 10  # 每小时最大调用次数
    
    # Step 3: 评估状态
    remaining_quota = max_quota - used_quota
    can_make_call = remaining_quota > 0
    
    # Step 4: 预测下次可用时间
    if not can_make_call:
        next_available = current_hour + timedelta(hours=1)
    else:
        next_available = None
    
    return QuotaStatus(
        can_make_call=can_make_call,
        used_quota=used_quota,
        remaining_quota=remaining_quota,
        next_available=next_available
    )
```

### 2.3 智能调用决策逻辑
```python
def decide_api_call_strategy(seed_words: List[str]) -> CallStrategy:
    """
    决定API调用策略
    
    逻辑步骤:
    1. 检查缓存覆盖率
    2. 评估API配额状态
    3. 计算批处理效益
    4. 选择最优策略
    """
    
    # Step 1: 缓存检查
    cache_coverage = calculate_cache_coverage(seed_words)
    uncached_words = [word for word in seed_words if not is_cached(word)]
    
    # Step 2: 配额检查
    quota_status = check_api_quota_availability()
    
    # Step 3: 策略决策
    if cache_coverage >= 0.8:  # 80%以上有缓存
        return CallStrategy.USE_CACHE_PRIMARILY
    
    elif quota_status.can_make_call and len(uncached_words) <= quota_status.remaining_quota:
        return CallStrategy.API_CALL_BATCH
    
    elif quota_status.can_make_call and len(uncached_words) > quota_status.remaining_quota:
        return CallStrategy.PARTIAL_API_CALL
    
    else:
        return CallStrategy.CACHE_ONLY
```

## 3. 批处理优化逻辑

### 3.1 种子词分组逻辑
```python
def group_seed_words_for_batching(seed_words: List[str]) -> List[List[str]]:
    """
    将种子词分组以优化API调用
    
    逻辑步骤:
    1. 按语义相似性分组
    2. 按长度和复杂度分组
    3. 优化批次大小
    4. 平衡负载分布
    """
    
    # Step 1: 语义相似性分组
    semantic_groups = group_by_semantic_similarity(seed_words)
    
    # Step 2: 优化批次大小
    optimized_batches = []
    for group in semantic_groups:
        # 每个API调用最多处理10个种子词
        batch_size = min(10, len(group))
        
        for i in range(0, len(group), batch_size):
            batch = group[i:i + batch_size]
            optimized_batches.append(batch)
    
    # Step 3: 负载平衡
    balanced_batches = balance_batch_loads(optimized_batches)
    
    return balanced_batches
```

### 3.2 API请求构建逻辑
```python
def build_keyword_planner_request(seed_words: List[str], context: ExpansionContext) -> KeywordPlannerRequest:
    """
    构建Google Keywords Planner API请求
    
    逻辑步骤:
    1. 设置基础请求参数
    2. 配置种子词列表
    3. 设置地理和语言定位
    4. 配置返回数据选项
    """
    
    # Step 1: 基础参数
    request = KeywordPlannerRequest()
    request.customer_id = get_customer_id()
    
    # Step 2: 种子词配置
    request.seed_keywords = [
        KeywordSeed(text=word.strip()) for word in seed_words
    ]
    
    # Step 3: 定位配置
    request.geo_target_constants = ["2840"]  # United States
    request.language_constants = ["1000"]    # English
    
    # Step 4: 返回数据配置
    request.keyword_plan_network = KeywordPlanNetwork.GOOGLE_SEARCH
    request.include_adult_keywords = False
    request.page_size = 50  # 每个种子词最多返回50个关键词
    
    return request
```

## 4. 响应处理逻辑

### 4.1 API响应解析逻辑
```python
def parse_keyword_planner_response(response: KeywordPlannerResponse, seed_word: str) -> List[ExpandedKeyword]:
    """
    解析Google Keywords Planner API响应
    
    逻辑步骤:
    1. 验证响应格式
    2. 提取关键词数据
    3. 标准化指标数据
    4. 创建ExpandedKeyword对象
    """
    
    # Step 1: 响应验证
    if not response or not response.keyword_ideas:
        log_warning(f"Empty response for seed word: {seed_word}")
        return []
    
    # Step 2: 数据提取
    expanded_keywords = []
    
    for idea in response.keyword_ideas:
        # Step 3: 指标标准化
        search_volume = normalize_search_volume(idea.keyword_idea_metrics.avg_monthly_searches)
        competition = normalize_competition_level(idea.keyword_idea_metrics.competition)
        cpc = normalize_cpc_value(idea.keyword_idea_metrics.suggested_bid_micros)
        
        # Step 4: 对象创建
        expanded_keyword = ExpandedKeyword(
            seed_word=seed_word,
            keyword=idea.text,
            search_volume=search_volume,
            competition=competition,
            cpc=cpc,
            source="google_keyword_planner",
            timestamp=datetime.now()
        )
        
        expanded_keywords.append(expanded_keyword)
    
    return expanded_keywords
```

### 4.2 数据质量过滤逻辑
```python
def filter_expanded_keywords(keywords: List[ExpandedKeyword], context: ExpansionContext) -> List[ExpandedKeyword]:
    """
    过滤和优化扩展关键词
    
    逻辑步骤:
    1. 去除重复关键词
    2. 过滤低质量关键词
    3. 应用相关性过滤
    4. 限制数量和排序
    """
    
    # Step 1: 去重
    unique_keywords = remove_duplicate_keywords(keywords)
    
    # Step 2: 质量过滤
    quality_filtered = []
    for keyword in unique_keywords:
        if meets_quality_criteria(keyword):
            quality_filtered.append(keyword)
    
    # Step 3: 相关性过滤
    relevance_filtered = []
    for keyword in quality_filtered:
        relevance_score = calculate_relevance_score(keyword, context)
        if relevance_score >= 0.6:  # 60%相关性阈值
            keyword.relevance_score = relevance_score
            relevance_filtered.append(keyword)
    
    # Step 4: 排序和限制
    sorted_keywords = sorted(relevance_filtered, 
                           key=lambda x: (x.relevance_score, x.search_volume), 
                           reverse=True)
    
    # 每个种子词最多保留30个扩展关键词
    limited_keywords = sorted_keywords[:30]
    
    return limited_keywords
```

## 5. 缓存管理逻辑

### 5.1 缓存键生成逻辑
```python
def generate_cache_key(seed_words: List[str], context: ExpansionContext) -> str:
    """
    生成API响应的缓存键
    
    逻辑步骤:
    1. 标准化种子词列表
    2. 包含上下文信息
    3. 生成唯一哈希
    4. 添加版本标识
    """
    
    # Step 1: 标准化
    normalized_words = [word.lower().strip() for word in seed_words]
    normalized_words.sort()  # 确保顺序一致性
    
    # Step 2: 上下文信息
    context_info = {
        "geo_target": "US",
        "language": "en",
        "network": "google_search"
    }
    
    # Step 3: 生成哈希
    cache_data = {
        "seed_words": normalized_words,
        "context": context_info,
        "version": "1.0"
    }
    
    cache_string = json.dumps(cache_data, sort_keys=True)
    cache_key = hashlib.md5(cache_string.encode()).hexdigest()
    
    return f"api_expansion_{cache_key}"
```

### 5.2 缓存有效性检查逻辑
```python
def is_cache_valid(cache_entry: CacheEntry) -> bool:
    """
    检查缓存条目的有效性
    
    逻辑步骤:
    1. 检查时间有效性
    2. 验证数据完整性
    3. 检查版本兼容性
    4. 评估数据质量
    """
    
    # Step 1: 时间检查
    cache_age = datetime.now() - cache_entry.timestamp
    if cache_age > timedelta(hours=24):
        return False
    
    # Step 2: 数据完整性
    if not cache_entry.data or len(cache_entry.data) == 0:
        return False
    
    # Step 3: 版本兼容性
    if cache_entry.version != "1.0":
        return False
    
    # Step 4: 数据质量
    if cache_entry.data_quality_score < 0.7:
        return False
    
    return True
```

## 6. 错误处理和恢复逻辑

### 6.1 API错误分类处理
```python
def handle_api_expansion_error(error: Exception, seed_words: List[str]) -> ExpansionResult:
    """
    处理API扩展过程中的错误
    
    错误分类和处理策略:
    1. 配额超限 → 使用缓存数据
    2. 网络错误 → 重试机制
    3. 认证错误 → 停止处理并报告
    4. 数据格式错误 → 部分结果处理
    """
    
    if isinstance(error, QuotaExceededError):
        # 使用缓存数据
        cached_results = get_cached_expansion_results(seed_words)
        if cached_results:
            return ExpansionResult(success=True, data=cached_results, source="cache")
        else:
            return ExpansionResult(success=False, error="No cache available, quota exceeded")
    
    elif isinstance(error, NetworkError):
        # 重试机制
        return retry_api_call_with_backoff(expand_keywords_via_api, seed_words, max_retries=3)
    
    elif isinstance(error, AuthenticationError):
        # 致命错误，停止处理
        log_critical(f"API authentication failed: {error}")
        return ExpansionResult(success=False, error="Authentication failed", fatal=True)
    
    elif isinstance(error, DataFormatError):
        # 尝试部分解析
        partial_results = parse_partial_response(error.partial_data, seed_words)
        return ExpansionResult(success=True, data=partial_results, warning="Partial data only")
    
    else:
        # 未知错误
        log_error(f"Unexpected API error: {error}")
        return ExpansionResult(success=False, error=str(error))
```

### 6.2 降级处理逻辑
```python
def fallback_expansion_strategy(seed_words: List[str]) -> List[ExpandedKeyword]:
    """
    API不可用时的降级扩展策略
    
    降级方法:
    1. 使用历史数据模式
    2. 基于规则的扩展
    3. 同义词词典扩展
    4. 最小化结果集
    """
    
    fallback_keywords = []
    
    # Method 1: 历史数据模式
    for seed_word in seed_words:
        historical_patterns = get_historical_expansion_patterns(seed_word)
        if historical_patterns:
            fallback_keywords.extend(apply_historical_patterns(seed_word, historical_patterns))
    
    # Method 2: 规则扩展
    rule_based_keywords = apply_rule_based_expansion(seed_words)
    fallback_keywords.extend(rule_based_keywords)
    
    # Method 3: 同义词扩展
    synonym_keywords = expand_with_synonyms(seed_words)
    fallback_keywords.extend(synonym_keywords)
    
    # 去重和质量过滤
    filtered_keywords = filter_and_deduplicate(fallback_keywords)
    
    return filtered_keywords[:20]  # 限制降级结果数量
```

## 7. 性能监控逻辑

### 7.1 API性能跟踪
```python
def track_api_performance(call_start: datetime, call_end: datetime, 
                         request_size: int, response_size: int) -> None:
    """
    跟踪API调用性能指标
    
    监控指标:
    1. 响应时间
    2. 吞吐量
    3. 成功率
    4. 数据质量
    """
    
    # 计算性能指标
    response_time = (call_end - call_start).total_seconds()
    throughput = response_size / response_time if response_time > 0 else 0
    
    # 记录性能数据
    performance_record = APIPerformanceRecord(
        timestamp=call_start,
        response_time=response_time,
        request_size=request_size,
        response_size=response_size,
        throughput=throughput
    )
    
    # 保存性能记录
    save_performance_record(performance_record)
    
    # 性能警告检查
    if response_time > 30.0:  # 30秒超时警告
        log_warning(f"Slow API response: {response_time}s")
```

这个详细的API扩展逻辑设计确保了在严格的API限制下仍能高效地扩展关键词，同时提供了完整的错误处理和性能优化策略。
