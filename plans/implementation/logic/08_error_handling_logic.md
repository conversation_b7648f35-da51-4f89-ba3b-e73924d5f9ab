# Error Handling Logic - 错误处理逻辑
# Version: 1.0
# Date: 2025-07-17
# Phase: 1.8 Logic Design & Verification

## 1. 错误处理架构概述

### 1.1 错误分类体系
```python
class ErrorCategory(Enum):
    FATAL = "fatal"           # 致命错误，停止所有处理
    RECOVERABLE = "recoverable"  # 可恢复错误，重试或降级
    DATA = "data"            # 数据错误，跳过并继续
    WARNING = "warning"      # 警告，记录但继续处理
```

### 1.2 错误处理原则
- **错误隔离**: 单个组件的错误不影响其他组件
- **渐进式恢复**: 多层次的恢复策略
- **详细记录**: 完整的错误上下文和堆栈信息
- **用户友好**: 向用户提供清晰的错误信息

## 2. 错误检测和分类逻辑

### 2.1 错误检测逻辑
```python
def detect_and_classify_error(exception: Exception, context: ErrorContext) -> ErrorClassification:
    """
    检测和分类错误
    
    逻辑步骤:
    1. 分析异常类型
    2. 检查错误上下文
    3. 评估影响范围
    4. 确定错误级别
    """
    
    # Step 1: 异常类型分析
    error_type = type(exception).__name__
    
    # Step 2: 上下文分析
    component = context.component
    operation = context.operation
    
    # Step 3: 错误分类
    if isinstance(exception, (ConfigurationError, AuthenticationError)):
        category = ErrorCategory.FATAL
        severity = ErrorSeverity.CRITICAL
        
    elif isinstance(exception, (APIRateLimitError, NetworkTimeoutError)):
        category = ErrorCategory.RECOVERABLE
        severity = ErrorSeverity.HIGH
        
    elif isinstance(exception, (DataValidationError, FormatError)):
        category = ErrorCategory.DATA
        severity = ErrorSeverity.MEDIUM
        
    elif isinstance(exception, (PerformanceWarning, CacheWarning)):
        category = ErrorCategory.WARNING
        severity = ErrorSeverity.LOW
        
    else:
        # 未知错误，保守处理
        category = ErrorCategory.FATAL
        severity = ErrorSeverity.CRITICAL
    
    # Step 4: 创建分类结果
    classification = ErrorClassification(
        category=category,
        severity=severity,
        error_type=error_type,
        component=component,
        operation=operation,
        recoverable=category != ErrorCategory.FATAL
    )
    
    return classification
```

### 2.2 错误上下文收集逻辑
```python
def collect_error_context(exception: Exception, operation_context: Dict) -> ErrorContext:
    """
    收集错误发生时的完整上下文信息
    
    逻辑步骤:
    1. 收集系统状态信息
    2. 记录操作参数
    3. 获取堆栈跟踪
    4. 收集环境信息
    """
    
    # Step 1: 系统状态
    system_state = {
        "timestamp": datetime.now().isoformat(),
        "memory_usage": get_memory_usage(),
        "cpu_usage": get_cpu_usage(),
        "disk_space": get_disk_space()
    }
    
    # Step 2: 操作参数
    operation_params = {
        "component": operation_context.get("component", "unknown"),
        "operation": operation_context.get("operation", "unknown"),
        "input_data": sanitize_sensitive_data(operation_context.get("input_data")),
        "configuration": operation_context.get("configuration", {})
    }
    
    # Step 3: 堆栈跟踪
    stack_trace = traceback.format_exc()
    
    # Step 4: 环境信息
    environment_info = {
        "python_version": sys.version,
        "platform": platform.platform(),
        "working_directory": os.getcwd(),
        "environment_variables": get_relevant_env_vars()
    }
    
    # 创建错误上下文
    error_context = ErrorContext(
        exception=exception,
        system_state=system_state,
        operation_params=operation_params,
        stack_trace=stack_trace,
        environment_info=environment_info
    )
    
    return error_context
```

## 3. 错误恢复策略逻辑

### 3.1 重试机制逻辑
```python
def execute_with_retry(operation: Callable, context: RetryContext) -> OperationResult:
    """
    带重试机制的操作执行
    
    逻辑步骤:
    1. 执行操作
    2. 检查是否需要重试
    3. 应用退避策略
    4. 记录重试历史
    """
    
    max_retries = context.max_retries
    base_delay = context.base_delay
    backoff_factor = context.backoff_factor
    
    for attempt in range(max_retries + 1):
        try:
            # Step 1: 执行操作
            result = operation()
            
            # 成功则返回结果
            log_info(f"Operation succeeded on attempt {attempt + 1}")
            return OperationResult(success=True, data=result, attempts=attempt + 1)
            
        except Exception as e:
            # Step 2: 检查是否应该重试
            if not should_retry(e, attempt, max_retries):
                log_error(f"Operation failed after {attempt + 1} attempts: {e}")
                return OperationResult(success=False, error=str(e), attempts=attempt + 1)
            
            # Step 3: 应用退避策略
            delay = calculate_backoff_delay(base_delay, backoff_factor, attempt)
            log_warning(f"Attempt {attempt + 1} failed, retrying in {delay}s: {e}")
            
            # Step 4: 等待后重试
            time.sleep(delay)
    
    # 所有重试都失败
    return OperationResult(success=False, error="Max retries exceeded", attempts=max_retries + 1)
```

### 3.2 降级处理逻辑
```python
def apply_degradation_strategy(error: Exception, context: DegradationContext) -> DegradationResult:
    """
    应用降级处理策略
    
    逻辑步骤:
    1. 评估降级选项
    2. 选择最佳降级策略
    3. 执行降级操作
    4. 验证降级结果
    """
    
    # Step 1: 评估可用的降级选项
    available_strategies = evaluate_degradation_options(error, context)
    
    # Step 2: 选择策略
    if DegradationStrategy.USE_CACHE in available_strategies:
        # 优先使用缓存数据
        strategy = DegradationStrategy.USE_CACHE
        
    elif DegradationStrategy.PARTIAL_PROCESSING in available_strategies:
        # 部分处理
        strategy = DegradationStrategy.PARTIAL_PROCESSING
        
    elif DegradationStrategy.DEFAULT_VALUES in available_strategies:
        # 使用默认值
        strategy = DegradationStrategy.DEFAULT_VALUES
        
    else:
        # 无可用降级策略
        return DegradationResult(success=False, error="No degradation strategy available")
    
    # Step 3: 执行降级操作
    try:
        degraded_result = execute_degradation_strategy(strategy, context)
        
        # Step 4: 验证结果
        if validate_degraded_result(degraded_result):
            log_warning(f"Applied degradation strategy: {strategy}")
            return DegradationResult(
                success=True, 
                data=degraded_result, 
                strategy=strategy,
                quality_score=calculate_quality_score(degraded_result)
            )
        else:
            return DegradationResult(success=False, error="Degraded result validation failed")
            
    except Exception as degradation_error:
        log_error(f"Degradation strategy failed: {degradation_error}")
        return DegradationResult(success=False, error=str(degradation_error))
```

## 4. 组件特定错误处理逻辑

### 4.1 LLM调用错误处理
```python
def handle_llm_errors(error: Exception, context: LLMContext) -> LLMErrorResult:
    """
    处理LLM调用相关错误
    
    错误类型和处理策略:
    1. API限制错误 → 使用缓存或等待
    2. 内容过滤错误 → 调整提示词
    3. 格式错误 → 使用备用解析
    4. 超时错误 → 重试或降级
    """
    
    if isinstance(error, APIRateLimitError):
        # 检查缓存
        cached_result = get_cached_llm_result(context.cache_key)
        if cached_result:
            return LLMErrorResult(success=True, data=cached_result, source="cache")
        
        # 等待限制重置
        wait_time = error.reset_time - datetime.now()
        if wait_time.total_seconds() < 300:  # 5分钟内
            time.sleep(wait_time.total_seconds())
            return retry_llm_call(context)
        else:
            return LLMErrorResult(success=False, error="Rate limit exceeded, wait time too long")
    
    elif isinstance(error, ContentFilterError):
        # 调整提示词并重试
        adjusted_prompt = adjust_prompt_for_content_filter(context.prompt)
        context.prompt = adjusted_prompt
        return retry_llm_call(context)
    
    elif isinstance(error, ResponseFormatError):
        # 使用备用解析方法
        parsed_result = parse_with_fallback_method(error.raw_response)
        if parsed_result:
            return LLMErrorResult(success=True, data=parsed_result, source="fallback_parsing")
        else:
            return LLMErrorResult(success=False, error="All parsing methods failed")
    
    elif isinstance(error, TimeoutError):
        # 重试或降级
        if context.retry_count < 2:
            context.retry_count += 1
            context.timeout *= 1.5  # 增加超时时间
            return retry_llm_call(context)
        else:
            return apply_llm_degradation(context)
    
    else:
        return LLMErrorResult(success=False, error=f"Unhandled LLM error: {error}")
```

### 4.2 API调用错误处理
```python
def handle_api_errors(error: Exception, context: APIContext) -> APIErrorResult:
    """
    处理外部API调用错误
    
    错误类型和处理策略:
    1. 网络错误 → 重试机制
    2. 认证错误 → 检查凭证
    3. 配额错误 → 使用缓存
    4. 服务错误 → 降级处理
    """
    
    if isinstance(error, NetworkError):
        # 网络错误重试
        retry_context = RetryContext(
            max_retries=3,
            base_delay=1.0,
            backoff_factor=2.0
        )
        return execute_with_retry(lambda: call_api(context), retry_context)
    
    elif isinstance(error, AuthenticationError):
        # 认证错误
        log_critical(f"API authentication failed for {context.api_name}: {error}")
        
        # 尝试刷新凭证
        if refresh_api_credentials(context.api_name):
            return retry_api_call(context)
        else:
            return APIErrorResult(success=False, error="Authentication failed", fatal=True)
    
    elif isinstance(error, QuotaExceededError):
        # 配额超限
        cached_result = get_cached_api_result(context.cache_key)
        if cached_result:
            return APIErrorResult(success=True, data=cached_result, source="cache")
        else:
            return APIErrorResult(success=False, error="Quota exceeded, no cache available")
    
    elif isinstance(error, ServiceUnavailableError):
        # 服务不可用
        degradation_result = apply_api_degradation(context)
        return APIErrorResult(
            success=degradation_result.success,
            data=degradation_result.data,
            source="degradation"
        )
    
    else:
        return APIErrorResult(success=False, error=f"Unhandled API error: {error}")
```

### 4.3 数据处理错误处理
```python
def handle_data_errors(error: Exception, context: DataContext) -> DataErrorResult:
    """
    处理数据相关错误
    
    错误类型和处理策略:
    1. 格式错误 → 数据清理
    2. 验证错误 → 跳过或修复
    3. 编码错误 → 自动转换
    4. 缺失错误 → 使用默认值
    """
    
    if isinstance(error, DataFormatError):
        # 尝试数据清理和修复
        try:
            cleaned_data = clean_and_repair_data(context.raw_data)
            validated_data = validate_data(cleaned_data)
            return DataErrorResult(success=True, data=validated_data, source="cleaned")
        except Exception as clean_error:
            log_warning(f"Data cleaning failed: {clean_error}")
            return DataErrorResult(success=False, error="Data format cannot be repaired")
    
    elif isinstance(error, ValidationError):
        # 验证错误处理
        if context.skip_invalid:
            # 跳过无效数据
            valid_data = filter_valid_data(context.raw_data)
            return DataErrorResult(success=True, data=valid_data, source="filtered")
        else:
            # 尝试修复
            repaired_data = attempt_data_repair(context.raw_data, error.validation_rules)
            if repaired_data:
                return DataErrorResult(success=True, data=repaired_data, source="repaired")
            else:
                return DataErrorResult(success=False, error="Data validation failed")
    
    elif isinstance(error, EncodingError):
        # 编码错误
        try:
            decoded_data = auto_detect_and_decode(context.raw_data)
            return DataErrorResult(success=True, data=decoded_data, source="re_encoded")
        except Exception as encoding_error:
            return DataErrorResult(success=False, error=f"Encoding repair failed: {encoding_error}")
    
    elif isinstance(error, MissingDataError):
        # 缺失数据
        if context.use_defaults:
            filled_data = fill_missing_with_defaults(context.raw_data, error.missing_fields)
            return DataErrorResult(success=True, data=filled_data, source="defaults")
        else:
            return DataErrorResult(success=False, error="Required data missing")
    
    else:
        return DataErrorResult(success=False, error=f"Unhandled data error: {error}")
```

## 5. 错误记录和监控逻辑

### 5.1 结构化错误记录
```python
def log_structured_error(error: Exception, context: ErrorContext, classification: ErrorClassification) -> None:
    """
    记录结构化错误信息
    
    逻辑步骤:
    1. 创建错误记录
    2. 添加上下文信息
    3. 分类和标记
    4. 持久化存储
    """
    
    # Step 1: 创建基础错误记录
    error_record = {
        "timestamp": datetime.now().isoformat(),
        "error_id": generate_error_id(),
        "error_type": type(error).__name__,
        "error_message": str(error),
        "category": classification.category.value,
        "severity": classification.severity.value
    }
    
    # Step 2: 添加上下文
    error_record.update({
        "component": context.component,
        "operation": context.operation,
        "stack_trace": context.stack_trace,
        "system_state": context.system_state,
        "environment": context.environment_info
    })
    
    # Step 3: 添加分类信息
    error_record.update({
        "recoverable": classification.recoverable,
        "impact_scope": assess_impact_scope(error, context),
        "resolution_strategy": suggest_resolution_strategy(classification)
    })
    
    # Step 4: 持久化存储
    save_error_record(error_record)
    
    # 发送告警（如果需要）
    if classification.severity in [ErrorSeverity.CRITICAL, ErrorSeverity.HIGH]:
        send_error_alert(error_record)
```

### 5.2 错误趋势分析
```python
def analyze_error_trends() -> ErrorTrendAnalysis:
    """
    分析错误趋势和模式
    
    逻辑步骤:
    1. 加载历史错误数据
    2. 分析错误频率
    3. 识别错误模式
    4. 生成趋势报告
    """
    
    # Step 1: 加载数据
    error_history = load_error_history(days=7)  # 最近7天
    
    # Step 2: 频率分析
    error_frequency = analyze_error_frequency(error_history)
    
    # Step 3: 模式识别
    error_patterns = identify_error_patterns(error_history)
    
    # Step 4: 生成报告
    trend_analysis = ErrorTrendAnalysis(
        total_errors=len(error_history),
        error_rate=calculate_error_rate(error_history),
        most_common_errors=error_frequency[:5],
        critical_patterns=error_patterns,
        recommendations=generate_error_recommendations(error_patterns)
    )
    
    return trend_analysis
```

这个全面的错误处理逻辑设计确保了系统在各种异常情况下都能保持稳定运行，并提供了完整的错误恢复和监控机制。
