# Atomic Decomposition Logic - 原子化拆解逻辑
# Version: 1.0
# Date: 2025-07-17
# Phase: 1.8 Logic Design & Verification

## 1. 逻辑概述

### 1.1 功能目标
将生物医学产品名称（如"Human IL-6 ELISA Kit"）智能拆解为有意义的种子关键词，为后续扩展提供基础。

### 1.2 核心算法
- **LLM驱动拆解**: 使用Google Gemini 2.5 Flash进行智能分析
- **上下文感知**: 基于产品类别和描述提供上下文
- **质量评估**: 对提取的种子词进行置信度评分
- **类型分类**: 将种子词分类为技术词、产品词、目标词、应用词

## 2. 详细逻辑流程

### 2.1 输入验证逻辑
```python
def validate_product_input(product: ProductData) -> ValidationResult:
    """
    验证产品数据的完整性和有效性
    
    逻辑步骤:
    1. 检查必需字段存在性
    2. 验证产品名称长度 (3-200字符)
    3. 检查特殊字符和编码
    4. 验证产品类别有效性
    
    前置条件: product 不为 None
    后置条件: 返回验证结果，包含错误详情
    """
    
    # Step 1: 必需字段检查
    if not product.product_name or product.product_name.strip() == "":
        return ValidationResult(False, "Product name is required")
    
    # Step 2: 长度验证
    name_length = len(product.product_name.strip())
    if name_length < 3:
        return ValidationResult(False, f"Product name too short: {name_length} chars")
    if name_length > 200:
        return ValidationResult(False, f"Product name too long: {name_length} chars")
    
    # Step 3: 字符验证
    if not is_valid_text_encoding(product.product_name):
        return ValidationResult(False, "Invalid text encoding in product name")
    
    # Step 4: 类别验证
    if not is_valid_category(product.product_category):
        return ValidationResult(False, f"Invalid category: {product.product_category}")
    
    return ValidationResult(True, "Validation passed")
```

### 2.2 上下文创建逻辑
```python
def create_decomposition_context(product: ProductData) -> DecompositionContext:
    """
    为产品创建拆解上下文信息
    
    逻辑步骤:
    1. 分析产品类别特征
    2. 提取产品描述关键信息
    3. 识别技术术语模式
    4. 构建上下文提示
    """
    
    # Step 1: 类别特征分析
    category_hints = get_category_decomposition_hints(product.product_category)
    
    # Step 2: 描述信息提取
    description_keywords = extract_description_keywords(product.product_details)
    
    # Step 3: 技术术语识别
    technical_patterns = identify_technical_patterns(product.product_name)
    
    # Step 4: 上下文构建
    context = DecompositionContext(
        product=product,
        category_hints=category_hints,
        description_keywords=description_keywords,
        technical_patterns=technical_patterns,
        domain="biomedical"
    )
    
    return context
```

### 2.3 LLM提示词生成逻辑
```python
def generate_decomposition_prompt(context: DecompositionContext) -> str:
    """
    生成用于LLM的拆解提示词
    
    逻辑步骤:
    1. 构建基础提示模板
    2. 注入产品特定信息
    3. 添加类别相关指导
    4. 设置输出格式要求
    """
    
    # Step 1: 基础模板
    base_template = load_prompt_template("atomic_decomposition_base")
    
    # Step 2: 产品信息注入
    product_info = {
        "product_name": context.product.product_name,
        "category": context.product.product_category,
        "description": context.description_keywords
    }
    
    # Step 3: 类别指导
    category_guidance = get_category_specific_guidance(context.product.product_category)
    
    # Step 4: 格式要求
    output_format = """
    Please extract seed keywords and classify them:
    
    TECHNICAL: Scientific/medical terms (e.g., "ELISA", "IL-6")
    PRODUCT: Product type terms (e.g., "Kit", "Test", "Assay")
    TARGET: Target audience/application (e.g., "Human", "Clinical")
    APPLICATION: Use case terms (e.g., "Detection", "Measurement")
    
    Output format:
    TECHNICAL: keyword1, keyword2
    PRODUCT: keyword3, keyword4
    TARGET: keyword5
    APPLICATION: keyword6, keyword7
    """
    
    # 组合完整提示词
    full_prompt = base_template.format(
        product_info=product_info,
        category_guidance=category_guidance,
        output_format=output_format
    )
    
    return full_prompt
```

### 2.4 LLM响应解析逻辑
```python
def parse_llm_response(response: str, product_id: str) -> List[SeedKeyword]:
    """
    解析LLM响应并提取结构化种子词
    
    逻辑步骤:
    1. 清理和标准化响应文本
    2. 按类型分组解析关键词
    3. 验证关键词质量
    4. 创建SeedKeyword对象
    """
    
    # Step 1: 文本清理
    cleaned_response = clean_llm_response(response)
    
    # Step 2: 分组解析
    keyword_groups = parse_keyword_groups(cleaned_response)
    
    # Step 3: 质量验证和对象创建
    seed_keywords = []
    
    for keyword_type, keywords in keyword_groups.items():
        for keyword in keywords:
            # 验证关键词质量
            if is_valid_keyword(keyword):
                # 计算置信度
                confidence = calculate_keyword_confidence(keyword, keyword_type, product_id)
                
                # 创建种子词对象
                seed_keyword = SeedKeyword(
                    keyword=keyword.strip(),
                    type=KeywordType(keyword_type.lower()),
                    confidence=confidence,
                    source="llm_decomposition"
                )
                
                seed_keywords.append(seed_keyword)
    
    return seed_keywords
```

### 2.5 置信度计算逻辑
```python
def calculate_keyword_confidence(keyword: str, keyword_type: str, product_id: str) -> float:
    """
    计算种子词的置信度分数
    
    逻辑步骤:
    1. 基础分数计算 (长度、字符质量)
    2. 类型匹配度评估
    3. 领域相关性检查
    4. 综合分数计算
    """
    
    # Step 1: 基础分数 (0.0-0.4)
    base_score = 0.0
    
    # 长度评分
    length = len(keyword)
    if 2 <= length <= 15:
        base_score += 0.2
    elif 16 <= length <= 25:
        base_score += 0.1
    
    # 字符质量评分
    if keyword.isalpha() or contains_valid_special_chars(keyword):
        base_score += 0.2
    
    # Step 2: 类型匹配度 (0.0-0.3)
    type_score = evaluate_type_match(keyword, keyword_type)
    
    # Step 3: 领域相关性 (0.0-0.3)
    domain_score = evaluate_biomedical_relevance(keyword)
    
    # Step 4: 综合分数
    total_score = base_score + type_score + domain_score
    
    # 确保分数在有效范围内
    return max(0.0, min(1.0, total_score))
```

### 2.6 关键词验证逻辑
```python
def validate_seed_keywords(keywords: List[SeedKeyword]) -> List[SeedKeyword]:
    """
    验证和过滤种子关键词
    
    逻辑步骤:
    1. 去除重复关键词
    2. 过滤低质量关键词
    3. 限制每种类型的数量
    4. 排序优化
    """
    
    # Step 1: 去重
    unique_keywords = remove_duplicates(keywords)
    
    # Step 2: 质量过滤
    quality_filtered = [kw for kw in unique_keywords if kw.confidence >= 0.3]
    
    # Step 3: 数量限制
    limited_keywords = limit_keywords_by_type(quality_filtered, {
        KeywordType.TECHNICAL: 5,
        KeywordType.PRODUCT: 3,
        KeywordType.TARGET: 2,
        KeywordType.APPLICATION: 4
    })
    
    # Step 4: 按置信度排序
    sorted_keywords = sorted(limited_keywords, key=lambda x: x.confidence, reverse=True)
    
    return sorted_keywords
```

## 3. 错误处理逻辑

### 3.1 LLM调用错误处理
```python
def handle_llm_decomposition_error(error: Exception, product: ProductData) -> DecompositionResult:
    """
    处理LLM调用过程中的错误
    
    错误类型和处理策略:
    1. API限制错误 → 使用缓存或降级处理
    2. 网络超时 → 重试机制
    3. 响应格式错误 → 使用备用解析方法
    4. 内容过滤错误 → 调整提示词重试
    """
    
    if isinstance(error, APIRateLimitError):
        # 检查缓存
        cached_result = get_cached_decomposition(product.product_id)
        if cached_result:
            return cached_result
        
        # 降级到基于规则的拆解
        return fallback_rule_based_decomposition(product)
    
    elif isinstance(error, NetworkTimeoutError):
        # 重试机制
        return retry_with_backoff(decompose_with_llm, product, max_retries=3)
    
    elif isinstance(error, ResponseFormatError):
        # 备用解析
        return parse_with_fallback_method(error.response, product.product_id)
    
    else:
        # 记录错误并返回空结果
        log_error(f"Unexpected decomposition error: {error}", product.product_id)
        return DecompositionResult(success=False, error=str(error))
```

### 3.2 数据质量错误处理
```python
def handle_data_quality_issues(product: ProductData) -> ProductData:
    """
    处理产品数据质量问题
    
    处理策略:
    1. 编码问题 → 自动修复
    2. 格式问题 → 标准化处理
    3. 缺失信息 → 使用默认值
    4. 异常字符 → 清理处理
    """
    
    # 编码修复
    if has_encoding_issues(product.product_name):
        product.product_name = fix_encoding(product.product_name)
    
    # 格式标准化
    product.product_name = normalize_product_name(product.product_name)
    
    # 缺失信息补充
    if not product.product_category:
        product.product_category = infer_category_from_name(product.product_name)
    
    # 异常字符清理
    product.product_name = clean_special_characters(product.product_name)
    
    return product
```

## 4. 性能优化逻辑

### 4.1 批处理优化
```python
def batch_decompose_products(products: List[ProductData], batch_size: int = 5) -> List[DecompositionResult]:
    """
    批量处理产品拆解以提高效率
    
    优化策略:
    1. 合并相似产品的提示词
    2. 并行处理独立批次
    3. 智能缓存检查
    4. 错误隔离
    """
    
    results = []
    
    # 按相似性分组
    product_groups = group_similar_products(products)
    
    for group in product_groups:
        # 检查缓存
        cached_results = check_batch_cache(group)
        
        # 处理未缓存的产品
        uncached_products = [p for p in group if p.product_id not in cached_results]
        
        if uncached_products:
            # 批量处理
            batch_results = process_product_batch(uncached_products)
            results.extend(batch_results)
        
        # 添加缓存结果
        results.extend(cached_results.values())
    
    return results
```

## 5. 验证和测试逻辑

### 5.1 单元测试覆盖点
- 输入验证逻辑的各种边界条件
- LLM响应解析的格式变化
- 置信度计算的准确性
- 错误处理的完整性

### 5.2 集成测试场景
- 完整拆解流程的端到端测试
- 不同产品类别的处理验证
- 错误恢复机制的有效性
- 性能基准测试

这个详细的逻辑设计为原子化拆解模块的实现提供了完整的指导，确保代码实现的准确性和健壮性。
