# Keyword Architect Agent - Code Structure Blueprint
**Version**: 1.0  
**Created**: 2025-07-17  
**Framework**: .clinerules v3.3.0  
**Phase**: 1.75 Implementation Blueprinting

## Project Directory Structure

```
keyword_architect_agent/
├── src/                          # Main source code
│   ├── __init__.py
│   ├── main.py                   # Main entry point
│   ├── config/                   # Configuration management
│   │   ├── __init__.py
│   │   ├── config_manager.py
│   │   └── settings.py
│   ├── services/                 # Core business services
│   │   ├── __init__.py
│   │   ├── csv_reader_service.py
│   │   ├── decomposition_service.py
│   │   ├── expansion_service.py
│   │   ├── association_service.py
│   │   └── storage_service.py
│   ├── clients/                  # External API clients
│   │   ├── __init__.py
│   │   ├── gemini_client.py
│   │   └── keyword_planner_client.py
│   ├── models/                   # Data models
│   │   ├── __init__.py
│   │   ├── product_models.py
│   │   ├── keyword_models.py
│   │   └── result_models.py
│   ├── utils/                    # Utility functions
│   │   ├── __init__.py
│   │   ├── logging_utils.py
│   │   ├── validation_utils.py
│   │   └── file_utils.py
│   └── orchestrator/             # Main processing orchestrator
│       ├── __init__.py
│       └── keyword_processor.py
├── config/                       # Configuration files
│   ├── google-ads.yaml          # Existing Google Ads config
│   └── deployment.config.toml   # Deployment configuration
├── source/                       # Input data
│   └── backed.csv               # Existing product data
├── results/                      # Output directory
│   ├── by_product/
│   ├── by_category/
│   ├── aggregated/
│   └── api_usage/
├── logs/                         # Log files
├── tests/                        # Test suites
│   ├── __init__.py
│   ├── unit/
│   ├── integration/
│   └── data/
├── docs/                         # Documentation
├── requirements.txt              # Python dependencies
├── setup.py                     # Package setup
├── README.md                     # Project documentation
└── .env.example                  # Environment variables template
```

## Core Module Implementations

### 1. Main Entry Point (`src/main.py`)

```python
#!/usr/bin/env python3
"""
Keyword Architect Agent - Main Entry Point
Orchestrates the three-stage keyword processing pipeline
"""

import click
import sys
from pathlib import Path
from typing import Optional

from src.config.config_manager import ConfigManager
from src.orchestrator.keyword_processor import KeywordProcessingOrchestrator
from src.utils.logging_utils import setup_logging


@click.command()
@click.option('--input-file', '-i', 
              type=click.Path(exists=True, path_type=Path),
              default='source/backed.csv',
              help='Input CSV file with product data')
@click.option('--output-dir', '-o',
              type=click.Path(path_type=Path),
              default='results',
              help='Output directory for results')
@click.option('--config-file', '-c',
              type=click.Path(exists=True, path_type=Path),
              default='config/deployment.config.toml',
              help='Configuration file')
@click.option('--batch-size', '-b',
              type=int,
              default=10,
              help='Number of products to process in each batch')
@click.option('--verbose', '-v',
              is_flag=True,
              help='Enable verbose logging')
def main(input_file: Path, output_dir: Path, config_file: Path, 
         batch_size: int, verbose: bool) -> None:
    """
    Keyword Architect Agent - Intelligent keyword research for biomedical products
    
    Processes product names through a three-stage pipeline:
    1. Atomic Decomposition (LLM-based seed keyword extraction)
    2. API Expansion (Google Keyword Planner integration)
    3. LLM Association (Semantic variation generation)
    """
    pass


def initialize_system(config_file: Path, verbose: bool) -> ConfigManager:
    """Initialize system configuration and logging"""
    pass


def validate_prerequisites(config: ConfigManager) -> bool:
    """Validate API keys and system prerequisites"""
    pass


def create_orchestrator(config: ConfigManager) -> KeywordProcessingOrchestrator:
    """Create and configure the main processing orchestrator"""
    pass


def process_keywords(orchestrator: KeywordProcessingOrchestrator,
                    input_file: Path, output_dir: Path, batch_size: int) -> bool:
    """Execute the main keyword processing pipeline"""
    pass


if __name__ == '__main__':
    main()
```

### 2. Configuration Manager (`src/config/config_manager.py`)

```python
"""
Configuration management for Keyword Architect Agent
Handles loading and validation of all system configurations
"""

import toml
from pathlib import Path
from typing import Dict, Any, Optional
from dataclasses import dataclass
import os

from src.models.result_models import ValidationResult


@dataclass
class GeminiConfig:
    """Configuration for Google Gemini API"""
    api_key: str
    model: str
    rate_limit_rpm: int
    timeout_seconds: int
    retry_attempts: int
    retry_backoff_factor: float


@dataclass
class KeywordPlannerConfig:
    """Configuration for Google Keyword Planner API"""
    config_file: Path
    customer_id: str
    rate_limit_strategy: str
    daily_request_limit: int
    timeout_seconds: int
    retry_attempts: int
    retry_backoff_factor: float


@dataclass
class ProcessingConfig:
    """Configuration for processing pipeline"""
    batch_size: int
    max_concurrent_requests: int
    memory_limit_mb: int
    temp_directory: str
    checkpoint_interval: int
    max_seed_keywords_per_product: int
    max_expanded_keywords_per_seed: int
    max_final_keywords_per_product: int
    keyword_relevance_threshold: float


class ConfigManager:
    """Central configuration manager for the system"""
    
    def __init__(self, config_file: Path):
        """Initialize configuration manager with config file path"""
        pass
    
    def load_configuration(self) -> bool:
        """Load all configuration from TOML file"""
        pass
    
    def get_gemini_config(self) -> GeminiConfig:
        """Get Google Gemini API configuration"""
        pass
    
    def get_keyword_planner_config(self) -> KeywordPlannerConfig:
        """Get Google Keyword Planner configuration"""
        pass
    
    def get_processing_config(self) -> ProcessingConfig:
        """Get processing pipeline configuration"""
        pass
    
    def validate_configuration(self) -> ValidationResult:
        """Validate all configuration settings"""
        pass
    
    def _load_environment_variables(self) -> Dict[str, str]:
        """Load environment variables for sensitive data"""
        pass
    
    def _validate_api_keys(self) -> bool:
        """Validate that all required API keys are present"""
        pass
```

### 3. Data Models (`src/models/`)

#### Product Models (`src/models/product_models.py`)

```python
"""
Data models for product information and processing context
"""

from dataclasses import dataclass
from typing import Dict, Any, Optional
from datetime import datetime


@dataclass
class ProductData:
    """Core product data structure"""
    product_id: str
    product_name: str
    product_category: str
    product_details: Dict[str, Any]
    raw_data: Dict[str, Any]
    
    def __post_init__(self):
        """Validate product data after initialization"""
        pass


@dataclass
class ProductContext:
    """Context information for product processing"""
    product_data: ProductData
    category_context: str
    target_market: str
    processing_hints: Dict[str, Any]
    
    @classmethod
    def from_product_data(cls, product_data: ProductData) -> 'ProductContext':
        """Create context from product data"""
        pass
```

#### Keyword Models (`src/models/keyword_models.py`)

```python
"""
Data models for keywords at different processing stages
"""

from dataclasses import dataclass
from typing import List, Optional
from enum import Enum
from datetime import datetime


class KeywordType(Enum):
    """Types of seed keywords"""
    TECHNICAL = "technical"
    PRODUCT = "product"
    TARGET = "target"
    APPLICATION = "application"


class KeywordCategory(Enum):
    """Categories of final keywords"""
    USER_INTENT = "user_intent"
    COMMERCIAL = "commercial"
    TECHNICAL = "technical"
    CONTEXTUAL = "contextual"


@dataclass
class SeedKeyword:
    """Seed keyword from atomic decomposition"""
    keyword: str
    type: KeywordType
    confidence: float
    source: str = "llm_decomposition"
    
    def __post_init__(self):
        """Validate seed keyword data"""
        pass


@dataclass
class ExpandedKeyword:
    """Expanded keyword from API processing"""
    keyword: str
    avg_monthly_searches: int
    competition: str
    competition_index: float
    source: str = "google_keyword_planner"
    
    def __post_init__(self):
        """Validate expanded keyword data"""
        pass


@dataclass
class FinalKeyword:
    """Final processed keyword with all metadata"""
    keyword: str
    category: KeywordCategory
    search_volume: int
    competition: str
    relevance_score: float
    sources: List[str]
    
    def __post_init__(self):
        """Validate final keyword data"""
        pass
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for serialization"""
        pass
```

### 4. Core Services (`src/services/`)

#### CSV Reader Service (`src/services/csv_reader_service.py`)

```python
"""
Service for reading and validating CSV product data
"""

import pandas as pd
from pathlib import Path
from typing import Iterator, List, Optional
import json

from src.models.product_models import ProductData
from src.models.result_models import ValidationResult
from src.utils.validation_utils import validate_required_columns


class CSVReaderService:
    """Service for reading and validating CSV product data"""
    
    def __init__(self, required_columns: List[str]):
        """Initialize CSV reader with required column specifications"""
        pass
    
    def validate_csv_file(self, file_path: Path) -> ValidationResult:
        """
        Validate CSV file structure and content
        
        Args:
            file_path: Path to CSV file to validate
            
        Returns:
            ValidationResult with success status and error details
        """
        pass
    
    def read_products(self, file_path: Path) -> Iterator[ProductData]:
        """
        Stream products from CSV file for memory efficiency
        
        Args:
            file_path: Path to CSV file
            
        Yields:
            ProductData objects for each valid row
        """
        pass
    
    def get_product_count(self, file_path: Path) -> int:
        """Get total number of products in CSV file"""
        pass
    
    def _parse_product_details(self, details_str: str) -> Dict[str, Any]:
        """Parse product details JSON string"""
        pass
    
    def _create_product_data(self, row: pd.Series) -> ProductData:
        """Create ProductData object from pandas Series"""
        pass
```

#### Atomic Decomposition Service (`src/services/decomposition_service.py`)

```python
"""
Service for decomposing product names into seed keywords using LLM
"""

from typing import List, Dict, Any
import asyncio

from src.clients.gemini_client import GeminiClient
from src.models.product_models import ProductData, ProductContext
from src.models.keyword_models import SeedKeyword, KeywordType
from src.models.result_models import DecompositionResult
from src.utils.logging_utils import get_logger


class AtomicDecompositionService:
    """Service for decomposing product names into seed keywords"""
    
    def __init__(self, gemini_client: GeminiClient, config: Dict[str, Any]):
        """Initialize decomposition service with LLM client and configuration"""
        pass
    
    def decompose_product(self, product: ProductData) -> DecompositionResult:
        """
        Decompose single product name into seed keywords
        
        Args:
            product: ProductData object to decompose
            
        Returns:
            DecompositionResult with seed keywords and metadata
        """
        pass
    
    def batch_decompose(self, products: List[ProductData]) -> List[DecompositionResult]:
        """
        Decompose multiple products in batch for efficiency
        
        Args:
            products: List of ProductData objects
            
        Returns:
            List of DecompositionResult objects
        """
        pass
    
    def _create_decomposition_prompt(self, product: ProductData) -> str:
        """Create LLM prompt for product decomposition"""
        pass
    
    def _parse_llm_response(self, response: str, product_id: str) -> List[SeedKeyword]:
        """Parse LLM response into structured seed keywords"""
        pass
    
    def _validate_seed_keywords(self, keywords: List[SeedKeyword]) -> List[SeedKeyword]:
        """Validate and filter seed keywords for quality"""
        pass
    
    def _classify_keyword_type(self, keyword: str, context: ProductContext) -> KeywordType:
        """Classify keyword into appropriate type category"""
        pass
```

### 5. API Clients (`src/clients/`)

#### Gemini Client (`src/clients/gemini_client.py`)

```python
"""
Client for Google Gemini 2.5 Flash API integration
"""

import google.generativeai as genai
from typing import Optional, Dict, Any
import time
import asyncio

from src.config.config_manager import GeminiConfig
from src.utils.logging_utils import get_logger


class GeminiClient:
    """Client for Google Gemini API interactions"""
    
    def __init__(self, config: GeminiConfig):
        """Initialize Gemini client with configuration"""
        pass
    
    def generate_text(self, prompt: str, context: Optional[Dict[str, Any]] = None) -> str:
        """
        Generate text using Gemini model
        
        Args:
            prompt: Text prompt for generation
            context: Optional context for generation
            
        Returns:
            Generated text response
        """
        pass
    
    def generate_text_with_retry(self, prompt: str, max_retries: int = 3) -> str:
        """Generate text with retry logic for reliability"""
        pass
    
    def validate_api_key(self) -> bool:
        """Validate API key by making a test request"""
        pass
    
    def _handle_rate_limiting(self) -> None:
        """Handle rate limiting with appropriate delays"""
        pass
    
    def _parse_response(self, response: Any) -> str:
        """Parse Gemini API response"""
        pass
```

### 6. Processing Orchestrator (`src/orchestrator/keyword_processor.py`)

```python
"""
Main orchestrator for the keyword processing pipeline
Coordinates all three stages of processing
"""

from typing import List, Dict, Any, Optional
from pathlib import Path
import time

from src.services.csv_reader_service import CSVReaderService
from src.services.decomposition_service import AtomicDecompositionService
from src.services.expansion_service import APIExpansionService
from src.services.association_service import LLMAssociationService
from src.services.storage_service import StorageManagerService
from src.models.product_models import ProductData
from src.models.result_models import ProcessingResult, ProcessingSummary
from src.utils.logging_utils import get_logger


class KeywordProcessingOrchestrator:
    """Main orchestrator for the keyword processing pipeline"""
    
    def __init__(self,
                 csv_reader: CSVReaderService,
                 decomposition_service: AtomicDecompositionService,
                 expansion_service: APIExpansionService,
                 association_service: LLMAssociationService,
                 storage_service: StorageManagerService):
        """Initialize orchestrator with all required services"""
        pass
    
    def process_products(self, input_file: Path, output_dir: Path, 
                        batch_size: int = 10) -> ProcessingSummary:
        """
        Process all products through the complete pipeline
        
        Args:
            input_file: Path to input CSV file
            output_dir: Directory for output results
            batch_size: Number of products per batch
            
        Returns:
            ProcessingSummary with overall results
        """
        pass
    
    def process_single_product(self, product: ProductData) -> ProcessingResult:
        """
        Process a single product through all three stages
        
        Args:
            product: ProductData to process
            
        Returns:
            ProcessingResult with final keywords and metadata
        """
        pass
    
    def _stage_a_decomposition(self, product: ProductData) -> List[SeedKeyword]:
        """Execute Stage A: Atomic Decomposition"""
        pass
    
    def _stage_b_expansion(self, seed_keywords: List[SeedKeyword], 
                          context: ProductContext) -> List[ExpandedKeyword]:
        """Execute Stage B: API-driven Expansion"""
        pass
    
    def _stage_c_association(self, expanded_keywords: List[ExpandedKeyword],
                           context: ProductContext) -> List[FinalKeyword]:
        """Execute Stage C: LLM-driven Association"""
        pass
    
    def _save_results(self, product_id: str, final_keywords: List[FinalKeyword],
                     metadata: Dict[str, Any]) -> bool:
        """Save processing results to storage"""
        pass
    
    def _create_processing_summary(self, results: List[ProcessingResult]) -> ProcessingSummary:
        """Create summary of processing results"""
        pass
```

## Function Signatures Summary

### Core Processing Functions
```python
# Main pipeline functions
def decompose_product_name(product: ProductData) -> List[SeedKeyword]
def expand_keywords(seed_keywords: List[str], context: ProductContext) -> List[ExpandedKeyword]
def generate_semantic_variations(expanded_keywords: List[ExpandedKeyword]) -> List[FinalKeyword]

# Utility functions
def validate_csv_file(file_path: Path) -> ValidationResult
def load_configuration(config_file: Path) -> ConfigManager
def setup_logging(config: LoggingConfig) -> None
def track_api_usage(api_name: str, request_details: Dict[str, Any]) -> None
```

### Data Transformation Functions
```python
# Data parsing and validation
def parse_product_details(details_json: str) -> Dict[str, Any]
def normalize_product_name(product_name: str) -> str
def validate_keyword_quality(keywords: List[SeedKeyword]) -> List[SeedKeyword]
def filter_by_relevance(keywords: List[FinalKeyword], threshold: float) -> List[FinalKeyword]

# Result aggregation
def aggregate_by_category(results: List[ProcessingResult]) -> Dict[str, List[FinalKeyword]]
def generate_processing_summary(results: List[ProcessingResult]) -> ProcessingSummary
def export_results(keywords: List[FinalKeyword], format: str) -> Path
```

This code structure blueprint provides a comprehensive foundation for implementing the Keyword Architect Agent system while maintaining simplicity and clear separation of concerns.
