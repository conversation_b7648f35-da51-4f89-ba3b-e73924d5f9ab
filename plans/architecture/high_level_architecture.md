# Keyword Architect Agent - High Level Architecture
# 关键词架构师代理 - 高层架构设计
# Version: 1.0
# Date: 2025-07-17

## 1. 架构概述

### 1.1 系统架构原则
- **简化优先**: 避免过度设计，保持系统简洁
- **功能函数化**: 每个核心功能实现为独立函数
- **统一存储**: 所有结果集中管理，便于检索
- **API节约**: 严格控制外部API调用，实现缓存机制
- **框架复用**: 使用AutoGen v0.4和prompt_manager避免重复造轮子

### 1.2 整体架构图
```
┌─────────────────────────────────────────────────────────────┐
│                    Keyword Architect Agent                  │
├─────────────────────────────────────────────────────────────┤
│  Input Layer                                                │
│  ┌─────────────────┐  ┌─────────────────┐                  │
│  │ source/         │  │ google-ads.yaml │                  │
│  │ backed.csv      │  │ (API Config)    │                  │
│  └─────────────────┘  └─────────────────┘                  │
├─────────────────────────────────────────────────────────────┤
│  Processing Layer                                           │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────┐ │
│  │ Decomposition   │  │ API Expansion   │  │ LLM         │ │
│  │ Module          │  │ Module          │  │ Association │ │
│  │                 │  │                 │  │ Module      │ │
│  │ Google Gemini   │  │ Keywords        │  │ Google      │ │
│  │ 2.5 Flash       │  │ Planner API     │  │ Gemini      │ │
│  └─────────────────┘  └─────────────────┘  └─────────────┘ │
├─────────────────────────────────────────────────────────────┤
│  Framework Layer                                            │
│  ┌─────────────────┐  ┌─────────────────┐                  │
│  │ AutoGen v0.4    │  │ Prompt Manager  │                  │
│  │ (LLM Requests)  │  │ (Prompt Mgmt)   │                  │
│  └─────────────────┘  └─────────────────┘                  │
├─────────────────────────────────────────────────────────────┤
│  Storage Layer                                              │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────┐ │
│  │ Results/        │  │ Cache/          │  │ Logs/       │ │
│  │ keyword_data.csv│  │ api_cache.json  │  │ api_logs    │ │
│  │ *_results.json  │  │ decomp_cache    │  │ error_logs  │ │
│  └─────────────────┘  └─────────────────┘  └─────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

## 2. 模块架构设计

### 2.1 原子化拆解模块 (Decomposition Module)
```
DecompositionModule
├── ProductLoader
│   ├── load_csv_data()
│   ├── validate_product_data()
│   └── prepare_batch()
├── GeminiDecomposer
│   ├── initialize_gemini_client()
│   ├── decompose_product_name()
│   └── extract_seed_words()
└── ResultsManager
    ├── save_decomposition_results()
    ├── cache_results()
    └── log_api_calls()
```

**核心职责**:
- 加载和验证CSV产品数据
- 使用Google Gemini 2.5 Flash进行智能拆解
- 提取和标准化种子词
- 缓存拆解结果避免重复处理

### 2.2 API驱动扩展模块 (API Expansion Module)
```
APIExpansionModule
├── KeywordsAPIClient
│   ├── initialize_google_ads_client()
│   ├── check_api_rate_limits()
│   └── call_keywords_planner()
├── KeywordExpander
│   ├── expand_seed_words()
│   ├── process_api_response()
│   └── filter_relevant_keywords()
└── CacheManager
    ├── check_cache()
    ├── save_to_cache()
    └── manage_cache_expiry()
```

**核心职责**:
- 管理Google Keywords Planner API调用
- 实现严格的API调用限制保护
- 扩展种子词为相关关键词列表
- 提供24小时缓存机制

### 2.3 LLM驱动联想模块 (LLM Association Module)
```
LLMAssociationModule
├── ScenarioManager
│   ├── define_association_scenarios()
│   ├── clinical_use_scenario()
│   ├── research_application_scenario()
│   └── commercial_search_scenario()
├── GeminiAssociator
│   ├── generate_semantic_associations()
│   ├── process_association_depth()
│   └── score_relevance()
└── QualityController
    ├── filter_duplicates()
    ├── validate_relevance()
    └── limit_associations_per_keyword()
```

**核心职责**:
- 定义多种联想场景（临床、研究、商业）
- 使用Google Gemini进行深层语义联想
- 实现多层次联想扩展
- 质量控制和相关性验证

### 2.4 结果管理模块 (Results Management Module)
```
ResultsManagementModule
├── DataConsolidator
│   ├── merge_all_results()
│   ├── standardize_format()
│   └── generate_final_table()
├── StorageManager
│   ├── save_csv_results()
│   ├── save_json_metadata()
│   └── manage_file_organization()
└── RetrievalService
    ├── search_by_product()
    ├── filter_by_type()
    └── export_results()
```

**核心职责**:
- 整合所有阶段的关键词数据
- 生成统一的CSV输出格式
- 提供数据检索和导出功能
- 维护数据完整性和一致性

## 3. 数据流架构

### 3.1 主要数据流
```
Input Data Flow:
source/backed.csv → ProductLoader → Validation → Batch Processing

Processing Data Flow:
Product Names → Decomposition → Seed Words → API Expansion → Keywords
                                          ↓
                                    LLM Association → Associated Keywords

Output Data Flow:
All Keywords → Consolidation → results/keyword_data.csv
API Calls → Logging → results/api_call_logs.json
Cache Data → cache/ directory
```

### 3.2 缓存策略
```
Cache Architecture:
├── API Response Cache (24h TTL)
│   ├── Google Ads API responses
│   └── Keyword expansion results
├── Decomposition Cache (7d TTL)
│   ├── Product name decompositions
│   └── Seed word extractions
└── Association Cache (24h TTL)
    ├── Semantic associations
    └── Scenario-based keywords
```

## 4. 技术栈架构

### 4.1 核心技术组件
```
Technology Stack:
├── Language: Python 3.9+
├── LLM Framework: AutoGen v0.4
├── Prompt Management: Custom prompt_manager
├── APIs:
│   ├── Google Gemini 2.5 Flash
│   └── Google Keywords Planner API
├── Data Processing: pandas, pyyaml
└── Storage: JSON, CSV files
```

### 4.2 依赖关系图
```
Dependency Graph:
Main Application
├── AutoGen v0.4 (LLM operations)
├── prompt_manager (prompt templates)
├── google-ads (Keywords Planner API)
├── pandas (data processing)
├── pyyaml (configuration)
└── python-dotenv (environment management)
```

## 5. 部署架构

### 5.1 文件组织结构
```
Project Structure:
keyword_architect_agent/
├── main.py (主入口)
├── keyword_architect_agent.intent.yaml
├── deployment.config.toml
├── google-ads.yaml
├── source/backed.csv
├── modules/
│   ├── decomposition.py
│   ├── api_expansion.py
│   ├── llm_association.py
│   └── results_management.py
├── prompt_manager/ (existing)
├── results/
├── cache/
└── logs/
```

### 5.2 运行时架构
```
Runtime Architecture:
┌─────────────────┐
│ Main Controller │
├─────────────────┤
│ ┌─────────────┐ │
│ │ Config Mgr  │ │
│ └─────────────┘ │
│ ┌─────────────┐ │
│ │ API Limiter │ │
│ └─────────────┘ │
│ ┌─────────────┐ │
│ │ Cache Mgr   │ │
│ └─────────────┘ │
│ ┌─────────────┐ │
│ │ Error Hdlr  │ │
│ └─────────────┘ │
└─────────────────┘
```

## 6. 安全架构

### 6.1 API安全
- API密钥通过环境变量管理
- 实现API调用频率限制
- 敏感数据不记录到日志

### 6.2 数据安全
- 输入数据验证和清理
- 输出数据格式标准化
- 错误信息不暴露敏感信息

## 7. 监控和日志架构

### 7.1 日志策略
```
Logging Architecture:
├── API Call Logs
│   ├── Request/Response details
│   ├── Rate limit status
│   └── Error conditions
├── Processing Logs
│   ├── Batch processing progress
│   ├── Data validation results
│   └── Performance metrics
└── Error Logs
    ├── Exception details
    ├── Stack traces
    └── Recovery actions
```

### 7.2 监控指标
- API调用成功率和延迟
- 数据处理吞吐量
- 缓存命中率
- 错误发生频率

## 8. 扩展性考虑

### 8.1 水平扩展
- 模块化设计支持独立扩展
- 批处理机制支持大数据量
- 缓存机制减少重复计算

### 8.2 功能扩展
- 新的联想场景易于添加
- 支持新的API数据源
- 输出格式可配置扩展
