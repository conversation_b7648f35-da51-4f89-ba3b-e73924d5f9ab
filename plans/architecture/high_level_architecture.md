# Keyword Architect Agent - High Level Architecture
# 关键词架构师代理 - 高层架构设计
# Version: 1.0
# Date: 2025-07-17

## 1. 架构概述

### 1.1 系统架构原则
- **简化优先**: 避免过度设计，保持系统简洁
- **功能函数化**: 每个核心功能实现为独立函数
- **统一存储**: 所有结果集中管理，便于检索
- **API节约**: 严格控制外部API调用，实现缓存机制
- **框架复用**: 使用AutoGen v0.4和prompt_manager避免重复造轮子

### 1.2 整体架构图
```
┌─────────────────────────────────────────────────────────────┐
│                    Keyword Architect Agent                  │
├─────────────────────────────────────────────────────────────┤
│  Input Layer                                                │
│  ┌─────────────────┐  ┌─────────────────┐                  │
│  │ source/         │  │ google-ads.yaml │                  │
│  │ backed.csv      │  │ (API Config)    │                  │
│  └─────────────────┘  └─────────────────┘                  │
├─────────────────────────────────────────────────────────────┤
│  Processing Layer                                           │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────┐ │
│  │ Decomposition   │  │ API Expansion   │  │ LLM         │ │
│  │ Module          │  │ Module          │  │ Association │ │
│  │ AutoGen+Gemini  │  │ API Limiter     │  │ Module      │ │
│  │ prompt_manager  │  │ 10calls/hour    │  │ AutoGen+    │ │
│  │                 │  │ Cache Priority  │  │ Gemini      │ │
│  └─────────────────┘  └─────────────────┘  └─────────────┘ │
├─────────────────────────────────────────────────────────────┤
│  Framework Layer (Required)                                 │
│  ┌─────────────────┐  ┌─────────────────┐                  │
│  │ AutoGen v0.4    │  │ prompt_manager  │                  │
│  │ (All LLM Ops)   │  │ (All Prompts)   │                  │
│  └─────────────────┘  └─────────────────┘                  │
├─────────────────────────────────────────────────────────────┤
│  Unified Storage Layer                                      │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────┐ │
│  │ results/        │  │ cache/          │  │ API Call    │ │
│  │ keyword_data.csv│  │ 24h_cache.json  │  │ History     │ │
│  │ final_table.csv │  │ llm_cache.json  │  │ Logs        │ │
│  │ metadata.json   │  │ auto_cleanup    │  │ (Critical)  │ │
│  └─────────────────┘  └─────────────────┘  └─────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

## 2. 模块架构设计

### 2.1 原子化拆解模块 (Decomposition Module)
```
DecompositionModule
├── ProductLoader
│   ├── load_csv_data()
│   ├── validate_product_data()
│   └── prepare_batch()
├── AutoGenGeminiAgent  # 基于AutoGen v0.4框架
│   ├── initialize_autogen_agent()
│   ├── load_prompt_templates()  # 集成prompt_manager
│   ├── decompose_with_autogen()
│   └── extract_seed_words()
└── CacheManager
    ├── check_decomposition_cache()
    ├── save_decomposition_cache()
    └── log_llm_calls()
```

**核心职责**:
- 加载和验证CSV产品数据
- 使用AutoGen v0.4 + Google Gemini 2.5 Flash进行智能拆解
- 通过prompt_manager系统管理提示词模板
- 提取和标准化种子词
- 缓存拆解结果避免重复LLM调用

### 2.2 API驱动扩展模块 (API Expansion Module)
```
APIExpansionModule
├── APICallLimiter  # 核心：API调用限制管理
│   ├── load_call_history()
│   ├── check_hourly_quota()  # 10次/小时严格限制
│   ├── record_api_call()
│   ├── calculate_next_available_time()
│   └── enforce_rate_limits()
├── KeywordsAPIClient
│   ├── initialize_google_ads_client()
│   ├── call_keywords_planner_with_protection()
│   └── process_api_response()
├── CacheFirstStrategy  # 缓存优先策略
│   ├── check_cache_before_api()
│   ├── save_api_results_to_cache()
│   └── manage_24h_cache_expiry()
└── KeywordExpander
    ├── expand_with_cache_priority()
    ├── batch_process_seed_words()
    └── filter_relevant_keywords()
```

**核心职责**:
- **严格管理Google Keywords Planner API调用限制（10次/小时）**
- **详细记录每次API调用历史**
- 实现缓存优先策略，最大化利用24小时缓存
- 扩展种子词为包含搜索量和竞争度的关键词列表

### 2.3 LLM驱动联想模块 (LLM Association Module)
```
LLMAssociationModule
├── AutoGenAssociationAgent  # 基于AutoGen v0.4框架
│   ├── initialize_autogen_association_agent()
│   ├── load_association_prompts()  # 使用prompt_manager
│   ├── generate_semantic_associations()
│   └── process_multi_scenario_associations()
├── ScenarioManager
│   ├── clinical_use_scenario()
│   ├── research_application_scenario()
│   ├── commercial_search_scenario()
│   └── load_scenario_templates()  # 从prompt_manager加载
├── QualityController
│   ├── filter_duplicates()
│   ├── validate_relevance()
│   ├── score_association_quality()
│   └── limit_associations_per_keyword()
└── CacheManager
    ├── check_association_cache()
    ├── save_association_cache()
    └── log_llm_calls()
```

**核心职责**:
- 使用AutoGen v0.4 + Google Gemini进行深层语义联想
- 通过prompt_manager管理多场景提示词模板
- 定义多种联想场景（临床、研究、商业）
- 实现多层次联想扩展和质量控制

### 2.4 统一存储管理模块 (Unified Storage Management Module)
```
UnifiedStorageModule
├── ResultsConsolidator
│   ├── merge_all_stage_results()
│   ├── standardize_data_format()
│   ├── generate_unified_table()
│   └── create_processing_summary()
├── UnifiedStorageManager  # 统一存储策略
│   ├── organize_results_directory()
│   ├── save_to_unified_location()  # results/目录统一存储
│   ├── save_final_csv()
│   ├── save_metadata_json()
│   └── cleanup_temp_files()  # 即时清理无用文件
├── APICallHistoryManager  # 专门管理API调用记录
│   ├── save_api_call_log()
│   ├── load_call_history()
│   ├── generate_usage_report()
│   └── archive_old_records()
└── RetrievalService
    ├── search_by_product()
    ├── filter_by_keyword_type()
    ├── export_to_multiple_formats()
    └── generate_summary_statistics()
```

**核心职责**:
- **统一存储所有结果到results/目录，易于寻找**
- 整合A、B、C三个阶段的关键词数据
- **专门管理Google Keywords Planner API调用历史记录**
- 生成最终的表格输出格式
- **即时清理无用的临时文件和测试文件**

## 3. 数据流架构

### 3.1 主要数据流
```
Input Data Flow:
source/backed.csv → ProductLoader → Validation → Batch Processing

Processing Data Flow:
Product Names → Decomposition → Seed Words → API Expansion → Keywords
                                          ↓
                                    LLM Association → Associated Keywords

Output Data Flow:
All Keywords → Consolidation → results/keyword_data.csv
API Calls → Logging → results/api_call_logs.json
Cache Data → cache/ directory
```

### 3.2 缓存策略
```
Cache Architecture:
├── API Response Cache (24h TTL)
│   ├── Google Ads API responses
│   └── Keyword expansion results
├── Decomposition Cache (7d TTL)
│   ├── Product name decompositions
│   └── Seed word extractions
└── Association Cache (24h TTL)
    ├── Semantic associations
    └── Scenario-based keywords
```

## 4. 技术栈架构

### 4.1 核心技术组件（严格遵循编码要求）
```
Technology Stack:
├── Language: Python 3.9+
├── 🔴 LLM Framework: AutoGen v0.4 (必须使用，所有LLM操作)
├── 🔴 Prompt Management: prompt_manager (必须使用，现有系统)
├── APIs:
│   ├── Google Gemini 2.5 Flash (通过AutoGen调用)
│   └── Google Keywords Planner API (严格限制10次/小时)
├── Data Processing: pandas, pyyaml
└── Storage: JSON, CSV files (统一存储到results/)
```

### 4.2 依赖关系图（避免重复造轮子）
```
Dependency Graph:
Main Application
├── 🔴 AutoGen v0.4 (所有LLM相关功能，禁止重复实现)
├── 🔴 prompt_manager (所有提示词管理，现有系统)
├── google-ads (Keywords Planner API，严格限制管理)
├── pandas (数据处理)
├── pyyaml (配置管理)
└── python-dotenv (环境变量)

约束条件:
- 禁止三层以上继承
- 禁止封装过度
- 必须使用AutoGen v0.4处理LLM
- 必须使用prompt_manager管理提示词
```

## 5. 部署架构

### 5.1 文件组织结构（符合项目要求）
```
Project Structure:
keyword_architect_agent/
├── main.py (唯一主入口，主目录文件最少)
├── keyword_architect_agent.intent.yaml
├── deployment.config.toml
├── google-ads.yaml
├── source/backed.csv
├── modules/
│   ├── decomposition.py (AutoGen+prompt_manager集成)
│   ├── api_expansion.py (API限制管理核心)
│   ├── llm_association.py (AutoGen+prompt_manager集成)
│   ├── unified_storage.py (统一存储管理)
│   ├── api_call_limiter.py (专门的API限制管理)
│   └── cache_manager.py (统一缓存管理)
├── prompt_manager/ (现有系统，必须使用)
├── knowledge/autogen_framework_reference/ (现有文档)
├── results/ (统一存储位置)
│   ├── keyword_data.csv (最终结果)
│   ├── api_call_history.json (API调用记录)
│   └── processing_metadata.json
├── cache/ (24小时缓存)
└── docs/ (生成的文档放置位置)
```

### 5.2 运行时架构
```
Runtime Architecture:
┌─────────────────┐
│ Main Controller │
├─────────────────┤
│ ┌─────────────┐ │
│ │ Config Mgr  │ │
│ └─────────────┘ │
│ ┌─────────────┐ │
│ │ API Limiter │ │
│ └─────────────┘ │
│ ┌─────────────┐ │
│ │ Cache Mgr   │ │
│ └─────────────┘ │
│ ┌─────────────┐ │
│ │ Error Hdlr  │ │
│ └─────────────┘ │
└─────────────────┘
```

## 6. 安全架构

### 6.1 API安全
- API密钥通过环境变量管理
- 实现API调用频率限制
- 敏感数据不记录到日志

### 6.2 数据安全
- 输入数据验证和清理
- 输出数据格式标准化
- 错误信息不暴露敏感信息

## 7. 监控和日志架构

### 7.1 日志策略
```
Logging Architecture:
├── API Call Logs
│   ├── Request/Response details
│   ├── Rate limit status
│   └── Error conditions
├── Processing Logs
│   ├── Batch processing progress
│   ├── Data validation results
│   └── Performance metrics
└── Error Logs
    ├── Exception details
    ├── Stack traces
    └── Recovery actions
```

### 7.2 监控指标
- API调用成功率和延迟
- 数据处理吞吐量
- 缓存命中率
- 错误发生频率

## 8. 扩展性考虑

### 8.1 水平扩展
- 模块化设计支持独立扩展
- 批处理机制支持大数据量
- 缓存机制减少重复计算

### 8.2 功能扩展
- 新的联想场景易于添加
- 支持新的API数据源
- 输出格式可配置扩展
