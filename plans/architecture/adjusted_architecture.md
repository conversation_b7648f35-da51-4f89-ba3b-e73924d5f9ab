# Keyword Architect Agent - 调整后的架构设计
# Version: 1.1 (调整版)
# Date: 2025-07-17
# 严格遵循项目要求和编码规范

## 🔴 关键调整说明

基于项目要求复查，以下是必须遵循的核心约束：

### 编码要求强制约束
1. **严禁重复造轮子** - 必须使用现有框架
2. **禁止封装过度** - 严禁三层及以上继承
3. **AutoGen v0.4强制使用** - 所有LLM功能必须通过AutoGen
4. **prompt_manager强制使用** - 所有提示词必须通过prompt_manager系统

### 项目要求强制约束
1. **主目录文件最少** - 除main.py外，主目录不要太多文件
2. **即时清理** - 无用测试文件和被淘汰文档需即时删除
3. **功能函数化** - 每个功能实现为一个函数
4. **统一存储** - 存储结果统一存放，易于寻找
5. **简化实现** - 功能要求较为简单，需要简化系统实现

## 🏗️ 调整后的架构设计

### 1. 简化的文件组织结构
```
keyword_architect_agent/
├── main.py                          # 唯一主入口（符合要求）
├── google-ads.yaml                  # 现有API配置
├── deployment.config.toml           # 部署配置
├── modules/                         # 核心模块（简化结构）
│   ├── decomposition.py            # A工作流：AutoGen+prompt_manager
│   ├── api_expansion.py            # B工作流：API限制管理核心
│   ├── llm_association.py          # C工作流：AutoGen+prompt_manager
│   ├── unified_storage.py          # 统一存储管理
│   └── api_limiter.py              # 专门的API限制管理
├── prompt_manager/                  # 现有系统（必须使用）
├── knowledge/autogen_framework_reference/  # 现有文档
├── source/backed.csv                # 输入数据
├── results/                         # 统一存储位置
│   ├── keyword_data.csv            # 最终结果表格
│   ├── api_call_history.json       # API调用记录（关键）
│   └── processing_metadata.json    # 处理元数据
└── cache/                          # 24小时缓存
```

### 2. 核心功能函数设计

#### A工作流：原子化拆解函数
```python
def decompose_product_names(products: List[ProductData]) -> List[SeedWord]:
    """
    使用AutoGen v0.4 + Google Gemini 2.5 Flash拆解产品名称
    
    强制要求:
    - 必须使用AutoGen v0.4框架
    - 必须使用prompt_manager管理提示词
    - 结果保存到统一存储位置
    """
    # 1. 初始化AutoGen代理
    agent = initialize_autogen_agent()
    
    # 2. 从prompt_manager加载提示词
    prompts = load_prompts_from_manager("decomposition")
    
    # 3. 批量处理产品
    seed_words = []
    for product in products:
        seeds = agent.decompose_with_prompt(product, prompts)
        seed_words.extend(seeds)
    
    # 4. 保存到统一存储
    save_to_unified_storage(seed_words, "decomposition_results")
    
    return seed_words
```

#### B工作流：API扩展函数（核心约束管理）
```python
def expand_keywords_via_api(seed_words: List[str]) -> List[ExpandedKeyword]:
    """
    使用Google Keywords Planner API扩展关键词
    
    关键约束:
    - API调用次数非常有限（10次/小时）
    - 必须记录每次调用历史
    - 优先使用缓存数据
    """
    # 1. 检查API调用配额
    if not check_api_quota():
        return get_cached_results(seed_words)
    
    # 2. 调用API并记录
    expanded_keywords = []
    for seed_word in seed_words:
        # 检查缓存
        cached = get_cached_expansion(seed_word)
        if cached:
            expanded_keywords.extend(cached)
            continue
            
        # API调用
        result = call_keywords_planner_api(seed_word)
        record_api_call(seed_word, result)  # 记录调用历史
        expanded_keywords.extend(result)
    
    # 3. 保存到统一存储
    save_to_unified_storage(expanded_keywords, "api_expansion_results")
    
    return expanded_keywords
```

#### C工作流：LLM联想函数
```python
def associate_keywords_via_llm(keywords: List[str]) -> List[AssociatedKeyword]:
    """
    使用AutoGen v0.4 + Google Gemini进行语义联想
    
    强制要求:
    - 必须使用AutoGen v0.4框架
    - 必须使用prompt_manager管理场景提示词
    """
    # 1. 初始化AutoGen联想代理
    agent = initialize_autogen_association_agent()
    
    # 2. 从prompt_manager加载场景提示词
    scenarios = load_prompts_from_manager("association_scenarios")
    
    # 3. 多场景联想
    associated_keywords = []
    for keyword in keywords:
        for scenario_name, prompt in scenarios.items():
            associations = agent.associate_with_scenario(keyword, prompt)
            associated_keywords.extend(associations)
    
    # 4. 保存到统一存储
    save_to_unified_storage(associated_keywords, "llm_association_results")
    
    return associated_keywords
```

#### 统一存储函数
```python
def consolidate_and_save_final_results(
    decomposed: List[SeedWord],
    expanded: List[ExpandedKeyword], 
    associated: List[AssociatedKeyword]
) -> str:
    """
    整合所有结果并保存为最终表格
    
    要求:
    - 统一存储到results/目录
    - 生成易于查找的文件结构
    - 即时清理临时文件
    """
    # 1. 整合数据
    final_table = merge_all_results(decomposed, expanded, associated)
    
    # 2. 保存最终表格
    output_path = "results/keyword_data.csv"
    final_table.to_csv(output_path, index=False)
    
    # 3. 保存元数据
    metadata = create_processing_metadata(decomposed, expanded, associated)
    save_json(metadata, "results/processing_metadata.json")
    
    # 4. 即时清理临时文件
    cleanup_temp_files()
    
    return output_path
```

### 3. API调用限制管理（核心模块）

```python
# api_limiter.py - 专门管理Google Keywords Planner API限制
class APICallLimiter:
    """
    Google Keywords Planner API调用限制管理器
    
    关键功能:
    - 严格控制10次/小时限制
    - 详细记录每次调用
    - 智能缓存策略
    """
    
    def check_hourly_quota(self) -> bool:
        """检查当前小时是否还有API调用配额"""
        history = self.load_call_history()
        current_hour_calls = self.count_current_hour_calls(history)
        return current_hour_calls < 10
    
    def record_api_call(self, seed_word: str, result: dict) -> None:
        """记录API调用历史（关键要求）"""
        call_record = {
            "timestamp": datetime.now().isoformat(),
            "seed_word": seed_word,
            "result_count": len(result.get("keywords", [])),
            "success": True
        }
        self.save_call_record(call_record)
    
    def get_next_available_time(self) -> datetime:
        """计算下次可以调用API的时间"""
        # 实现逻辑...
        pass
```

### 4. AutoGen v0.4集成模块

```python
# 必须使用AutoGen v0.4的集成模块
def initialize_autogen_agent() -> AutoGenAgent:
    """
    初始化AutoGen代理（强制要求）
    
    参考: knowledge/autogen_framework_reference/
    """
    from autogen import ConversableAgent
    
    agent = ConversableAgent(
        name="keyword_decomposer",
        llm_config={
            "model": "gemini-2.5-flash",
            "api_key": "AIzaSyBWPiNDni24gEzdZUzNUhVxws6prDHB4Yo"
        }
    )
    
    return agent

def load_prompts_from_manager(prompt_type: str) -> dict:
    """
    从prompt_manager系统加载提示词（强制要求）
    
    参考: prompt_manager_document/
    """
    from prompt_manager.core import PromptManager
    
    pm = PromptManager()
    return pm.load_prompts(prompt_type)
```

### 5. 主入口函数（main.py）

```python
#!/usr/bin/env python3
"""
Keyword Architect Agent - 主入口
严格遵循项目要求的简化实现
"""

def main():
    """
    主函数：运行A、B、C三个工作流程
    """
    # 1. 加载产品数据
    products = load_csv_data("source/backed.csv")
    
    # 2. A工作流：原子化拆解
    seed_words = decompose_product_names(products)
    
    # 3. B工作流：API驱动扩展
    expanded_keywords = expand_keywords_via_api(seed_words)
    
    # 4. C工作流：LLM驱动联想
    associated_keywords = associate_keywords_via_llm(expanded_keywords)
    
    # 5. 统一存储最终结果
    final_output = consolidate_and_save_final_results(
        seed_words, expanded_keywords, associated_keywords
    )
    
    print(f"处理完成，结果保存到: {final_output}")

if __name__ == "__main__":
    main()
```

## ✅ 调整后的架构优势

1. **严格遵循编码要求**：
   - 所有LLM功能通过AutoGen v0.4
   - 所有提示词通过prompt_manager
   - 避免重复造轮子和过度封装

2. **满足项目要求**：
   - 主目录文件最少（只有main.py等必要文件）
   - 功能完全函数化
   - 统一存储到results/目录
   - 简化的系统实现

3. **突出核心约束**：
   - Google Keywords Planner API限制管理是架构核心
   - API调用历史记录是关键功能
   - 缓存优先策略最大化利用有限API调用

这个调整后的架构完全符合项目要求和编码规范，为Phase 2的实现提供了明确的指导。
