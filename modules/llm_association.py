#!/usr/bin/env python3
"""
LLM Association Module - LLM驱动联想模块
使用AutoGen v0.4 + Google Gemini + prompt_manager进行关键词语义联想
"""

import json
from datetime import datetime
from typing import List, Dict, Any, Optional
from dataclasses import dataclass, asdict
from pathlib import Path

# 必须使用AutoGen v0.4
try:
    from autogen_agentchat.agents import AssistantAgent
    from autogen_agentchat.messages import TextMessage
    from autogen_ext.models.openai import OpenAIChatCompletionClient
except ImportError as e:
    print(f"[ERROR] AutoGen v0.4 导入失败: {e}")
    raise

# 必须使用prompt_manager
try:
    from prompt_manager.core import PromptManager
except ImportError as e:
    print(f"[ERROR] prompt_manager 导入失败: {e}")
    raise

from .unified_storage import save_to_unified_storage


@dataclass
class AssociatedKeyword:
    """联想关键词数据结构"""
    base_keyword: str
    associated_keyword: str
    association_type: str
    scenario: str
    relevance_score: float
    source: str
    timestamp: str


class AutoGenAssociationAgent:
    """
    基于AutoGen v0.4的关键词联想代理
    
    强制要求:
    - 使用AutoGen v0.4框架
    - 使用prompt_manager管理场景提示词
    """
    
    def __init__(self):
        """初始化AutoGen联想代理"""
        self.api_key = "AIzaSyBWPiNDni24gEzdZUzNUhVxws6prDHB4Yo"
        self.agent = None
        self.prompt_manager = None
        self.scenario_prompts = {}
        
        # 初始化组件
        self._initialize_prompt_manager()
        self._initialize_autogen_agent()
    
    def _initialize_prompt_manager(self) -> None:
        """初始化prompt_manager系统并创建场景提示词"""
        try:
            self.prompt_manager = PromptManager()
            
            # 创建多场景联想提示词模板
            self.scenario_prompts = {
                "clinical_use": """
你是生物医学领域的专家。请为关键词"{keyword}"生成临床使用场景的相关关键词。

重点关注:
- 临床诊断应用
- 疾病检测
- 患者样本类型
- 临床实验室使用
- 医生和技师的查询习惯

请生成10-15个相关关键词，每行一个，格式如下:
关键词1
关键词2
关键词3
...

只输出关键词列表，不要其他内容。
""",
                
                "research_application": """
你是生物医学研究专家。请为关键词"{keyword}"生成科研应用场景的相关关键词。

重点关注:
- 科研实验应用
- 研究方法和技术
- 实验室设备和试剂
- 学术研究查询
- 科研人员的搜索习惯

请生成10-15个相关关键词，每行一个，格式如下:
关键词1
关键词2
关键词3
...

只输出关键词列表，不要其他内容。
""",
                
                "commercial_search": """
你是生物医学产品营销专家。请为关键词"{keyword}"生成商业搜索场景的相关关键词。

重点关注:
- 产品采购查询
- 供应商搜索
- 产品比较
- 价格和规格查询
- 采购人员的搜索习惯

请生成10-15个相关关键词，每行一个，格式如下:
关键词1
关键词2
关键词3
...

只输出关键词列表，不要其他内容。
"""
            }
            
            print("[LLM_ASSOCIATION] prompt_manager 场景提示词初始化完成")
            
        except Exception as e:
            print(f"[ERROR] prompt_manager 初始化失败: {e}")
            raise
    
    def _initialize_autogen_agent(self) -> None:
        """初始化AutoGen v0.4联想代理"""
        try:
            # 创建模型客户端
            model_client = OpenAIChatCompletionClient(
                model="gemini-2.5-flash",
                api_key=self.api_key,
                base_url="https://generativelanguage.googleapis.com/v1beta/openai/"
            )
            
            # 创建AssistantAgent
            self.agent = AssistantAgent(
                name="keyword_associator",
                model_client=model_client,
                system_message="你是一个专业的生物医学关键词联想专家，专门负责为给定关键词生成相关的语义联想词。"
            )
            
            print("[LLM_ASSOCIATION] AutoGen v0.4 联想代理初始化完成")
            
        except Exception as e:
            print(f"[ERROR] AutoGen 联想代理初始化失败: {e}")
            raise
    
    def generate_scenario_associations(self, keyword: str, scenario: str) -> List[AssociatedKeyword]:
        """
        为特定场景生成关键词联想
        
        Args:
            keyword: 基础关键词
            scenario: 联想场景 (clinical_use, research_application, commercial_search)
            
        Returns:
            List[AssociatedKeyword]: 场景化联想结果
        """
        try:
            if scenario not in self.scenario_prompts:
                print(f"[WARNING] 未知场景: {scenario}")
                return []
            
            # 准备场景提示词
            prompt = self.scenario_prompts[scenario].format(keyword=keyword)
            
            print(f"[LLM_ASSOCIATION] 生成联想: {keyword} -> {scenario}")
            
            # 使用AutoGen进行联想（异步处理）
            import asyncio

            async def get_association_response():
                message = TextMessage(content=prompt, source="user")
                response = await self.agent.on_messages([message], cancellation_token=None)
                return response

            # 运行异步函数
            try:
                response = asyncio.run(get_association_response())
            except RuntimeError:
                # 如果已经在事件循环中，使用不同的方法
                loop = asyncio.get_event_loop()
                if loop.is_running():
                    import concurrent.futures
                    with concurrent.futures.ThreadPoolExecutor() as executor:
                        future = executor.submit(asyncio.run, get_association_response())
                        response = future.result()
                else:
                    response = asyncio.run(get_association_response())

            # 解析响应
            if hasattr(response, 'chat_message') and response.chat_message:
                response_text = response.chat_message.content
            else:
                response_text = str(response)
            
            # 解析联想关键词
            associated_keywords = self._parse_association_response(
                response_text, keyword, scenario
            )
            
            print(f"[LLM_ASSOCIATION] 场景 {scenario} 生成 {len(associated_keywords)} 个联想词")
            return associated_keywords
            
        except Exception as e:
            print(f"[ERROR] 场景联想失败: {e}")
            return []
    
    def _parse_association_response(self, response_text: str, base_keyword: str, scenario: str) -> List[AssociatedKeyword]:
        """
        解析AutoGen的联想响应
        
        Args:
            response_text: AutoGen的响应文本
            base_keyword: 基础关键词
            scenario: 联想场景
            
        Returns:
            List[AssociatedKeyword]: 解析后的联想关键词列表
        """
        associated_keywords = []
        current_time = datetime.now().isoformat()
        
        try:
            lines = response_text.strip().split('\n')
            
            for line in lines:
                line = line.strip()
                
                # 跳过空行和非关键词行
                if not line or len(line) < 2:
                    continue
                
                # 清理可能的编号或符号
                cleaned_keyword = line
                for prefix in ['- ', '* ', '• ', '1. ', '2. ', '3. ', '4. ', '5. ', '6. ', '7. ', '8. ', '9. ']:
                    if cleaned_keyword.startswith(prefix):
                        cleaned_keyword = cleaned_keyword[len(prefix):].strip()
                        break
                
                # 验证关键词有效性
                if cleaned_keyword and len(cleaned_keyword) > 1 and len(cleaned_keyword) < 100:
                    relevance_score = self._calculate_relevance_score(
                        base_keyword, cleaned_keyword, scenario
                    )
                    
                    associated_keyword = AssociatedKeyword(
                        base_keyword=base_keyword,
                        associated_keyword=cleaned_keyword,
                        association_type="semantic",
                        scenario=scenario,
                        relevance_score=relevance_score,
                        source="llm_association",
                        timestamp=current_time
                    )
                    associated_keywords.append(associated_keyword)
            
        except Exception as e:
            print(f"[WARNING] 解析联想响应失败: {e}")
            print(f"原始响应: {response_text}")
        
        return associated_keywords
    
    def _calculate_relevance_score(self, base_keyword: str, associated_keyword: str, scenario: str) -> float:
        """
        计算联想关键词的相关性得分
        
        Args:
            base_keyword: 基础关键词
            associated_keyword: 联想关键词
            scenario: 联想场景
            
        Returns:
            float: 相关性得分 (0.0-1.0)
        """
        score = 0.5  # 基础分数
        
        base_lower = base_keyword.lower()
        assoc_lower = associated_keyword.lower()
        
        # 词汇重叠度
        base_words = set(base_lower.split())
        assoc_words = set(assoc_lower.split())
        overlap = len(base_words & assoc_words)
        if overlap > 0:
            score += 0.2
        
        # 长度合理性
        if 3 <= len(associated_keyword) <= 50:
            score += 0.1
        
        # 场景相关性
        scenario_keywords = {
            "clinical_use": ["clinical", "diagnosis", "patient", "test", "assay", "detection"],
            "research_application": ["research", "experiment", "laboratory", "analysis", "study"],
            "commercial_search": ["buy", "purchase", "supplier", "price", "product", "kit"]
        }
        
        if scenario in scenario_keywords:
            for kw in scenario_keywords[scenario]:
                if kw in assoc_lower:
                    score += 0.1
                    break
        
        # 生物医学相关性
        biomedical_terms = ["elisa", "pcr", "assay", "antibody", "protein", "gene", "cell", "serum", "plasma"]
        if any(term in assoc_lower for term in biomedical_terms):
            score += 0.1
        
        return min(1.0, score)


def associate_keywords_via_llm(keywords: List[str]) -> List[AssociatedKeyword]:
    """
    使用AutoGen进行关键词语义联想（主要函数）
    
    Args:
        keywords: 基础关键词列表
        
    Returns:
        List[AssociatedKeyword]: 所有联想关键词列表
    """
    print(f"[LLM_ASSOCIATION] 开始联想 {len(keywords)} 个关键词...")
    
    # 初始化AutoGen联想代理
    agent = AutoGenAssociationAgent()
    
    all_associated_keywords = []
    scenarios = ["clinical_use", "research_application", "commercial_search"]
    
    for i, keyword in enumerate(keywords, 1):
        print(f"[LLM_ASSOCIATION] 处理进度: {i}/{len(keywords)} - {keyword}")
        
        for scenario in scenarios:
            try:
                associated_keywords = agent.generate_scenario_associations(keyword, scenario)
                all_associated_keywords.extend(associated_keywords)
                
            except Exception as e:
                print(f"[ERROR] 关键词 {keyword} 场景 {scenario} 联想失败: {e}")
                continue
    
    # 去重和质量过滤
    filtered_keywords = _filter_and_deduplicate(all_associated_keywords)
    
    # 保存联想结果
    association_data = [asdict(kw) for kw in filtered_keywords]
    save_to_unified_storage(association_data, "json", "llm_association_results.json")
    
    print(f"[LLM_ASSOCIATION] 联想完成，总计获得 {len(filtered_keywords)} 个高质量联想词")
    
    return filtered_keywords


def _filter_and_deduplicate(keywords: List[AssociatedKeyword]) -> List[AssociatedKeyword]:
    """
    过滤和去重联想关键词
    
    Args:
        keywords: 联想关键词列表
        
    Returns:
        List[AssociatedKeyword]: 过滤后的关键词列表
    """
    # 去重（基于关键词文本）
    seen = set()
    unique_keywords = []
    
    for kw in keywords:
        key = kw.associated_keyword.lower().strip()
        if key not in seen and len(key) > 2:
            seen.add(key)
            unique_keywords.append(kw)
    
    # 质量过滤（相关性得分 >= 0.6）
    quality_filtered = [kw for kw in unique_keywords if kw.relevance_score >= 0.6]
    
    # 按相关性得分排序
    quality_filtered.sort(key=lambda x: x.relevance_score, reverse=True)
    
    print(f"[LLM_ASSOCIATION] 过滤前: {len(keywords)}, 去重后: {len(unique_keywords)}, 质量过滤后: {len(quality_filtered)}")
    
    return quality_filtered


def get_keywords_from_expansion() -> List[str]:
    """
    从API扩展结果中提取关键词列表
    
    Returns:
        List[str]: 关键词列表
    """
    try:
        # 从统一存储加载扩展结果
        expansion_file = Path("results/api_expansion_results.json")
        if not expansion_file.exists():
            print("[WARNING] API扩展结果文件不存在")
            return []
        
        with open(expansion_file, 'r', encoding='utf-8') as f:
            expansion_data = json.load(f)
        
        # 提取关键词，去重，限制数量
        keywords = list(set([item['keyword'] for item in expansion_data if item.get('keyword')]))
        
        # 限制关键词数量以控制LLM调用成本
        if len(keywords) > 50:
            keywords = keywords[:50]
            print(f"[LLM_ASSOCIATION] 限制关键词数量为 50 个以控制成本")
        
        print(f"[LLM_ASSOCIATION] 从扩展结果中提取到 {len(keywords)} 个关键词")
        
        return keywords
        
    except Exception as e:
        print(f"[ERROR] 提取关键词失败: {e}")
        return []
