# Keyword Architect Agent - Project Intent
# 关键词架构师代理 - 项目意图文件
# Version: 1.0
# Date: 2025-07-17

project_metadata:
  name: "Keyword Architect Agent"
  chinese_name: "关键词架构师代理"
  version: "1.0.0"
  description: "生物医学产品关键词研究和扩展工具"
  domain: "生物医学产品营销"
  target_products: "ELISA试剂盒、过敏测试等生物医学产品"

business_context:
  problem_statement: |
    生物医学产品（如ELISA试剂盒、过敏测试）需要全面的关键词策略来提升搜索可见性和市场覆盖。
    传统的关键词研究方法无法充分挖掘产品的语义关联和用户查询模式。
  
  business_value: |
    - 提升产品搜索可见性
    - 扩大市场覆盖范围
    - 优化SEO和SEM策略
    - 发现潜在的用户查询模式

user_stories:
  - id: US001
    title: "产品名称原子化拆解"
    as_a: "营销人员"
    i_want: "将产品名称拆解为种子词"
    so_that: "可以获得基础的关键词组件"
    acceptance_criteria:
      - "能够处理CSV格式的产品数据"
      - "使用Google Gemini 2.5 Flash进行智能拆解"
      - "生成结构化的种子词列表"
      - "保存拆解结果供后续使用"

  - id: US002
    title: "API驱动关键词扩展"
    as_a: "营销人员"
    i_want: "基于种子词获取大量相关关键词"
    so_that: "可以发现更多潜在的搜索词汇"
    acceptance_criteria:
      - "集成Google Keywords Planner API"
      - "基于种子词进行关键词扩展"
      - "记录API调用历史（API调用次数有限）"
      - "返回关键词搜索量和竞争度数据"

  - id: US003
    title: "LLM驱动语义联想"
    as_a: "营销人员"
    i_want: "通过AI进行深层语义和场景联想"
    so_that: "可以发现API无法覆盖的用户查询模式"
    acceptance_criteria:
      - "使用Google Gemini 2.5 Flash进行语义分析"
      - "生成场景化的关键词建议"
      - "覆盖不同用户意图和查询场景"
      - "以表格形式输出最终结果"

  - id: US004
    title: "结果统一存储管理"
    as_a: "系统管理员"
    i_want: "所有生成的关键词数据统一存储"
    so_that: "便于查找、管理和后续分析"
    acceptance_criteria:
      - "建立统一的数据存储结构"
      - "支持结果的检索和导出"
      - "保持数据的完整性和一致性"

technical_constraints:
  apis:
    - name: "Google Gemini 2.5 Flash"
      api_key: "AIzaSyBWPiNDni24gEzdZUzNUhVxws6prDHB4Yo"
      usage: "产品拆解和语义联想"
      
    - name: "Google Keywords Planner API"
      config_file: "google-ads.yaml"
      usage: "关键词扩展"
      limitation: "API调用次数非常有限，需要记录调用历史"

  frameworks:
    - name: "AutoGen v0.4"
      usage: "LLM请求和Agent功能"
      reference: "knowledge/autogen_framework_reference"
      
    - name: "Prompt Manager"
      usage: "提示词管理"
      source: "prompt_manager/"
      documentation: "prompt_manager_document/"

  coding_rules:
    - "严禁重复造轮子"
    - "禁止封装过度，严禁三层及以上继承"
    - "使用AutoGen v0.4处理LLM相关功能"
    - "使用prompt_manager系统管理提示词"

system_requirements:
  architecture_principles:
    - "简化系统实现（功能要求较为简单）"
    - "主目录文件管理（除程序主入口外，主目录不要有太多文件）"
    - "即时清理（无用测试文件和被淘汰文档需即时删除）"
    - "功能函数化（每个功能实现为一个函数）"
    - "统一存储（存储结果统一存放，易于寻找）"

  file_organization:
    - "生成的文档放置在docs目录下"
    - "遵循knowledge/coding_extra_rules.md的编码要求"
    - "源数据存储在source/目录"
    - "配置文件统一管理"

data_flow:
  input:
    - "source/backed.csv - 产品数据表"
    - "google-ads.yaml - API配置"
    
  processing_stages:
    1. "原子化拆解：产品名 → 种子词"
    2. "API扩展：种子词 → 关键词列表"
    3. "语义联想：关键词 → 深层关联词"
    4. "结果整合：多源数据 → 统一表格"
    
  output:
    - "结构化关键词数据表"
    - "API调用记录"
    - "拆解和联想过程记录"

success_criteria:
  functional:
    - "能够成功处理source/backed.csv中的产品数据"
    - "生成全面的关键词策略"
    - "API调用记录完整准确"
    - "结果数据结构化且易于使用"
    
  non_functional:
    - "系统架构简洁清晰"
    - "代码可维护性高"
    - "文档组织规范"
    - "符合编码规范要求"

risks_and_mitigations:
  - risk: "Google Keywords Planner API调用次数限制"
    mitigation: "实现调用记录和缓存机制"
    
  - risk: "LLM API调用成本"
    mitigation: "优化提示词，减少不必要的调用"
    
  - risk: "数据处理错误"
    mitigation: "实现数据验证和错误处理机制"
