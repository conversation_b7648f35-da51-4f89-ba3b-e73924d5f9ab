[{"timestamp": "2025-07-17T11:42:16.130579", "seed_word": "ELISA", "success": false, "result_count": 0, "api_key_id": "api_1", "error_message": "API调用异常: Specified service LanguageConstantService\" does not exist in Google Ads API v20.", "response_time": 0.4530973434448242}, {"timestamp": "2025-07-17T11:47:16.766996", "seed_word": "ELISA", "success": false, "result_count": 0, "api_key_id": "api_1", "error_message": "API调用异常: Unknown field for GenerateKeywordIdeasRequest: language_constants", "response_time": 0.43679094314575195}, {"timestamp": "2025-07-17T11:50:24.543264", "seed_word": "ELISA", "success": false, "result_count": 0, "api_key_id": "api_1", "error_message": "API调用异常: 'str' object has no attribute 'append'", "response_time": 0.44921875}, {"timestamp": "2025-07-17T11:50:56.291474", "seed_word": "ELISA", "success": false, "result_count": 0, "api_key_id": "api_1", "error_message": "API调用异常: Unknown field for KeywordPlanHistoricalMetrics: suggested_bid_micros", "response_time": 3.4778385162353516}, {"timestamp": "2025-07-17T12:02:41.046912", "seed_word": "test_keyword_0", "success": true, "result_count": 5, "api_key_id": "api_1", "error_message": null, "response_time": null}, {"timestamp": "2025-07-17T12:02:41.047146", "seed_word": "test_keyword_1", "success": true, "result_count": 5, "api_key_id": "api_1", "error_message": null, "response_time": null}, {"timestamp": "2025-07-17T12:02:41.047466", "seed_word": "test_keyword_2", "success": true, "result_count": 5, "api_key_id": "api_1", "error_message": null, "response_time": null}, {"timestamp": "2025-07-17T12:02:41.047699", "seed_word": "test_keyword_3", "success": true, "result_count": 5, "api_key_id": "api_1", "error_message": null, "response_time": null}, {"timestamp": "2025-07-17T12:02:41.047898", "seed_word": "test_keyword_4", "success": true, "result_count": 5, "api_key_id": "api_1", "error_message": null, "response_time": null}, {"timestamp": "2025-07-17T12:02:41.048135", "seed_word": "test_keyword_5", "success": true, "result_count": 5, "api_key_id": "api_1", "error_message": null, "response_time": null}, {"timestamp": "2025-07-17T12:02:41.048325", "seed_word": "test_keyword_6", "success": true, "result_count": 5, "api_key_id": "api_1", "error_message": null, "response_time": null}, {"timestamp": "2025-07-17T12:02:41.048565", "seed_word": "test_keyword_7", "success": true, "result_count": 5, "api_key_id": "api_1", "error_message": null, "response_time": null}, {"timestamp": "2025-07-17T12:02:41.048882", "seed_word": "test_keyword_8", "success": true, "result_count": 5, "api_key_id": "api_1", "error_message": null, "response_time": null}, {"timestamp": "2025-07-17T12:02:41.049168", "seed_word": "test_keyword_9", "success": true, "result_count": 5, "api_key_id": "api_1", "error_message": null, "response_time": null}, {"timestamp": "2025-07-17T13:04:22.200950", "seed_word": "Allergy", "success": false, "result_count": 0, "api_key_id": "api_2", "error_message": "API调用异常: Unknown field for KeywordPlanHistoricalMetrics: suggested_bid_micros", "response_time": 3.4808506965637207}, {"timestamp": "2025-07-17T13:04:24.900436", "seed_word": "Intolerance", "success": false, "result_count": 0, "api_key_id": "api_2", "error_message": "API调用异常: Unknown field for KeywordPlanHistoricalMetrics: suggested_bid_micros", "response_time": 2.6981804370880127}, {"timestamp": "2025-07-17T13:04:30.442853", "seed_word": "Symptoms", "success": false, "result_count": 0, "api_key_id": "api_2", "error_message": "API调用异常: Unknown field for KeywordPlanHistoricalMetrics: suggested_bid_micros", "response_time": 5.5411975383758545}, {"timestamp": "2025-07-17T13:04:32.019742", "seed_word": "<PERSON><PERSON> Allergy", "success": false, "result_count": 0, "api_key_id": "api_2", "error_message": "API调用异常: Unknown field for KeywordPlanHistoricalMetrics: suggested_bid_micros", "response_time": 1.5756866931915283}, {"timestamp": "2025-07-17T13:04:34.856947", "seed_word": "Rapid Test", "success": false, "result_count": 0, "api_key_id": "api_2", "error_message": "API调用异常: Unknown field for KeywordPlanHistoricalMetrics: suggested_bid_micros", "response_time": 2.8360559940338135}, {"timestamp": "2025-07-17T13:04:37.730785", "seed_word": "Test Kit", "success": false, "result_count": 0, "api_key_id": "api_2", "error_message": "API调用异常: Unknown field for KeywordPlanHistoricalMetrics: suggested_bid_micros", "response_time": 2.8724822998046875}, {"timestamp": "2025-07-17T13:04:39.972415", "seed_word": "Home Kit", "success": false, "result_count": 0, "api_key_id": "api_2", "error_message": "API调用异常: Unknown field for KeywordPlanHistoricalMetrics: suggested_bid_micros", "response_time": 2.240320920944214}, {"timestamp": "2025-07-17T13:04:41.909864", "seed_word": "Diagnostic Kit", "success": false, "result_count": 0, "api_key_id": "api_2", "error_message": "API调用异常: Unknown field for KeywordPlanHistoricalMetrics: suggested_bid_micros", "response_time": 1.9365949630737305}, {"timestamp": "2025-07-17T13:04:45.452043", "seed_word": "<PERSON><PERSON>", "success": false, "result_count": 0, "api_key_id": "api_2", "error_message": "API调用异常: Unknown field for KeywordPlanHistoricalMetrics: suggested_bid_micros", "response_time": 3.54142427444458}, {"timestamp": "2025-07-17T13:04:48.125231", "seed_word": "Allergen", "success": false, "result_count": 0, "api_key_id": "api_2", "error_message": "API调用异常: Unknown field for KeywordPlanHistoricalMetrics: suggested_bid_micros", "response_time": 2.671835422515869}, {"timestamp": "2025-07-17T13:04:51.272494", "seed_word": "Food Allergy", "success": false, "result_count": 0, "api_key_id": "api_2", "error_message": "API调用异常: Unknown field for KeywordPlanHistoricalMetrics: suggested_bid_micros", "response_time": 3.146294593811035}, {"timestamp": "2025-07-17T13:04:53.636268", "seed_word": "Detection", "success": false, "result_count": 0, "api_key_id": "api_2", "error_message": "API调用异常: Unknown field for KeywordPlanHistoricalMetrics: suggested_bid_micros", "response_time": 2.3624987602233887}, {"timestamp": "2025-07-17T13:04:57.056186", "seed_word": "Screening", "success": false, "result_count": 0, "api_key_id": "api_2", "error_message": "API调用异常: Unknown field for KeywordPlanHistoricalMetrics: suggested_bid_micros", "response_time": 3.418583869934082}, {"timestamp": "2025-07-17T13:04:59.648688", "seed_word": "Home Testing", "success": false, "result_count": 0, "api_key_id": "api_2", "error_message": "API调用异常: Unknown field for KeywordPlanHistoricalMetrics: suggested_bid_micros", "response_time": 2.5910885334014893}, {"timestamp": "2025-07-17T13:05:02.204652", "seed_word": "Rapid Testing", "success": false, "result_count": 0, "api_key_id": "api_2", "error_message": "API调用异常: Unknown field for KeywordPlanHistoricalMetrics: suggested_bid_micros", "response_time": 2.5544779300689697}, {"timestamp": "2025-07-17T13:05:02.115817", "seed_word": "Allergy", "success": false, "result_count": 0, "api_key_id": "api_2", "error_message": "API调用异常: Unknown field for KeywordPlanHistoricalMetrics: suggested_bid_micros", "response_time": -0.09020066261291504}, {"timestamp": "2025-07-17T13:05:03.377174", "seed_word": "<PERSON>", "success": false, "result_count": 0, "api_key_id": "api_2", "error_message": "API调用异常: Unknown field for KeywordPlanHistoricalMetrics: suggested_bid_micros", "response_time": 1.2598540782928467}, {"timestamp": "2025-07-17T13:05:05.574341", "seed_word": "Pet Allergy", "success": false, "result_count": 0, "api_key_id": "api_2", "error_message": "API调用异常: Unknown field for KeywordPlanHistoricalMetrics: suggested_bid_micros", "response_time": 2.195579767227173}, {"timestamp": "2025-07-17T13:05:07.528845", "seed_word": "<PERSON><PERSON>", "success": false, "result_count": 0, "api_key_id": "api_2", "error_message": "API调用异常: Unknown field for KeywordPlanHistoricalMetrics: suggested_bid_micros", "response_time": 1.9530398845672607}, {"timestamp": "2025-07-17T13:05:08.914150", "seed_word": "Rapid Test", "success": false, "result_count": 0, "api_key_id": "api_2", "error_message": "API调用异常: Unknown field for KeywordPlanHistoricalMetrics: suggested_bid_micros", "response_time": 1.3838658332824707}, {"timestamp": "2025-07-17T13:05:10.595748", "seed_word": "Test Kit", "success": false, "result_count": 0, "api_key_id": "api_2", "error_message": "API调用异常: Unknown field for KeywordPlanHistoricalMetrics: suggested_bid_micros", "response_time": 1.680126428604126}, {"timestamp": "2025-07-17T13:05:11.505965", "seed_word": "Home Kit", "success": false, "result_count": 0, "api_key_id": "api_2", "error_message": "API调用异常: Unknown field for KeywordPlanHistoricalMetrics: suggested_bid_micros", "response_time": 0.9088194370269775}, {"timestamp": "2025-07-17T13:05:12.511536", "seed_word": "Diagnostic Kit", "success": false, "result_count": 0, "api_key_id": "api_2", "error_message": "API调用异常: Unknown field for KeywordPlanHistoricalMetrics: suggested_bid_micros", "response_time": 1.004138469696045}, {"timestamp": "2025-07-17T13:05:16.208045", "seed_word": "Cat", "success": false, "result_count": 0, "api_key_id": "api_2", "error_message": "API调用异常: Unknown field for KeywordPlanHistoricalMetrics: suggested_bid_micros", "response_time": 3.694730758666992}, {"timestamp": "2025-07-17T13:05:17.368502", "seed_word": "Allergen", "success": false, "result_count": 0, "api_key_id": "api_2", "error_message": "API调用异常: Unknown field for KeywordPlanHistoricalMetrics: suggested_bid_micros", "response_time": 1.158961534500122}, {"timestamp": "2025-07-17T13:05:21.892035", "seed_word": "Pet", "success": false, "result_count": 0, "api_key_id": "api_2", "error_message": "API调用异常: Unknown field for KeywordPlanHistoricalMetrics: suggested_bid_micros", "response_time": 4.522355079650879}, {"timestamp": "2025-07-17T13:05:23.088185", "seed_word": "Detection", "success": false, "result_count": 0, "api_key_id": "api_2", "error_message": "API调用异常: Unknown field for KeywordPlanHistoricalMetrics: suggested_bid_micros", "response_time": 1.194526195526123}, {"timestamp": "2025-07-17T13:05:24.265361", "seed_word": "Home Testing", "success": false, "result_count": 0, "api_key_id": "api_2", "error_message": "API调用异常: Unknown field for KeywordPlanHistoricalMetrics: suggested_bid_micros", "response_time": 1.1753911972045898}, {"timestamp": "2025-07-17T13:05:25.753794", "seed_word": "Rapid Testing", "success": false, "result_count": 0, "api_key_id": "api_2", "error_message": "API调用异常: Unknown field for KeywordPlanHistoricalMetrics: suggested_bid_micros", "response_time": 1.4867606163024902}, {"timestamp": "2025-07-17T13:05:26.777705", "seed_word": "Screening", "success": false, "result_count": 0, "api_key_id": "api_2", "error_message": "API调用异常: Unknown field for KeywordPlanHistoricalMetrics: suggested_bid_micros", "response_time": 1.0222880840301514}, {"timestamp": "2025-07-17T13:05:27.912397", "seed_word": "Allergy", "success": false, "result_count": 0, "api_key_id": "api_2", "error_message": "API调用异常: Unknown field for KeywordPlanHistoricalMetrics: suggested_bid_micros", "response_time": 1.1330807209014893}, {"timestamp": "2025-07-17T13:05:29.837967", "seed_word": "Shellfish Allergies", "success": false, "result_count": 0, "api_key_id": "api_2", "error_message": "API调用异常: Unknown field for KeywordPlanHistoricalMetrics: suggested_bid_micros", "response_time": 1.924555778503418}, {"timestamp": "2025-07-17T13:05:31.334881", "seed_word": "Food Allergy", "success": false, "result_count": 0, "api_key_id": "api_2", "error_message": "API调用异常: Unknown field for KeywordPlanHistoricalMetrics: suggested_bid_micros", "response_time": 1.495232105255127}, {"timestamp": "2025-07-17T13:05:32.374946", "seed_word": "Rapid Test", "success": false, "result_count": 0, "api_key_id": "api_2", "error_message": "API调用异常: Unknown field for KeywordPlanHistoricalMetrics: suggested_bid_micros", "response_time": 1.0382697582244873}, {"timestamp": "2025-07-17T13:05:33.599865", "seed_word": "Home Test", "success": false, "result_count": 0, "api_key_id": "api_2", "error_message": "API调用异常: Unknown field for KeywordPlanHistoricalMetrics: suggested_bid_micros", "response_time": 1.2231075763702393}, {"timestamp": "2025-07-17T13:05:34.656628", "seed_word": "Test Kit", "success": false, "result_count": 0, "api_key_id": "api_2", "error_message": "API调用异常: Unknown field for KeywordPlanHistoricalMetrics: suggested_bid_micros", "response_time": 1.055046796798706}, {"timestamp": "2025-07-17T13:06:56.484781", "seed_word": "Diagnostic Test", "success": false, "result_count": 0, "api_key_id": "api_2", "error_message": "API调用异常: Unknown field for KeywordPlanHistoricalMetrics: suggested_bid_micros", "response_time": 81.82635354995728}, {"timestamp": "2025-07-17T13:06:59.239086", "seed_word": "<PERSON><PERSON>", "success": false, "result_count": 0, "api_key_id": "api_2", "error_message": "API调用异常: Unknown field for KeywordPlanHistoricalMetrics: suggested_bid_micros", "response_time": 2.7524662017822266}, {"timestamp": "2025-07-17T13:07:01.505011", "seed_word": "Shellfish", "success": false, "result_count": 0, "api_key_id": "api_2", "error_message": "API调用异常: Unknown field for KeywordPlanHistoricalMetrics: suggested_bid_micros", "response_time": 2.264737606048584}, {"timestamp": "2025-07-17T13:07:04.204673", "seed_word": "Allergen", "success": false, "result_count": 0, "api_key_id": "api_2", "error_message": "API调用异常: Unknown field for KeywordPlanHistoricalMetrics: suggested_bid_micros", "response_time": 2.697718381881714}, {"timestamp": "2025-07-17T13:07:05.357054", "seed_word": "Detection", "success": false, "result_count": 0, "api_key_id": "api_2", "error_message": "API调用异常: Unknown field for KeywordPlanHistoricalMetrics: suggested_bid_micros", "response_time": 1.1504909992218018}, {"timestamp": "2025-07-17T13:07:06.449617", "seed_word": "Rapid Testing", "success": false, "result_count": 0, "api_key_id": "api_2", "error_message": "API调用异常: Unknown field for KeywordPlanHistoricalMetrics: suggested_bid_micros", "response_time": 1.0905463695526123}, {"timestamp": "2025-07-17T13:07:06.471768", "seed_word": "Home Testing", "success": false, "result_count": 0, "api_key_id": "api_2", "error_message": "API调用异常: Unknown field for KeywordPlanHistoricalMetrics: suggested_bid_micros", "response_time": 0.020263671875}, {"timestamp": "2025-07-17T13:07:07.467689", "seed_word": "Screening", "success": false, "result_count": 0, "api_key_id": "api_2", "error_message": "API调用异常: Unknown field for KeywordPlanHistoricalMetrics: suggested_bid_micros", "response_time": 0.993863582611084}, {"timestamp": "2025-07-17T13:07:08.499564", "seed_word": "Dermatophagoides <PERSON>", "success": false, "result_count": 0, "api_key_id": "api_2", "error_message": "API调用异常: Unknown field for KeywordPlanHistoricalMetrics: suggested_bid_micros", "response_time": 1.0299837589263916}, {"timestamp": "2025-07-17T13:07:10.628838", "seed_word": "Dust Mite Allergy", "success": false, "result_count": 0, "api_key_id": "api_2", "error_message": "API调用异常: Unknown field for KeywordPlanHistoricalMetrics: suggested_bid_micros", "response_time": 2.1272292137145996}, {"timestamp": "2025-07-17T13:07:11.614044", "seed_word": "Allergy", "success": false, "result_count": 0, "api_key_id": "api_2", "error_message": "API调用异常: Unknown field for KeywordPlanHistoricalMetrics: suggested_bid_micros", "response_time": 0.983313798904419}, {"timestamp": "2025-07-17T13:07:14.321081", "seed_word": "Dust Mite", "success": false, "result_count": 0, "api_key_id": "api_2", "error_message": "API调用异常: Unknown field for KeywordPlanHistoricalMetrics: suggested_bid_micros", "response_time": 2.704951047897339}, {"timestamp": "2025-07-17T13:07:15.810624", "seed_word": "Rapid Test", "success": false, "result_count": 0, "api_key_id": "api_2", "error_message": "API调用异常: Unknown field for KeywordPlanHistoricalMetrics: suggested_bid_micros", "response_time": 1.4875452518463135}, {"timestamp": "2025-07-17T13:07:18.292058", "seed_word": "Home Test", "success": false, "result_count": 0, "api_key_id": "api_2", "error_message": "API调用异常: Unknown field for KeywordPlanHistoricalMetrics: suggested_bid_micros", "response_time": 2.4791951179504395}, {"timestamp": "2025-07-17T13:07:19.427360", "seed_word": "Test Kit", "success": false, "result_count": 0, "api_key_id": "api_2", "error_message": "API调用异常: Unknown field for KeywordPlanHistoricalMetrics: suggested_bid_micros", "response_time": 1.1331384181976318}, {"timestamp": "2025-07-17T13:07:22.065882", "seed_word": "Diagnostic Test", "success": false, "result_count": 0, "api_key_id": "api_2", "error_message": "API调用异常: Unknown field for KeywordPlanHistoricalMetrics: suggested_bid_micros", "response_time": 2.6363484859466553}, {"timestamp": "2025-07-17T13:07:23.177368", "seed_word": "Allergen", "success": false, "result_count": 0, "api_key_id": "api_2", "error_message": "API调用异常: Unknown field for KeywordPlanHistoricalMetrics: suggested_bid_micros", "response_time": 1.1091711521148682}, {"timestamp": "2025-07-17T13:07:24.301184", "seed_word": "Dust Mite", "success": false, "result_count": 0, "api_key_id": "api_2", "error_message": "API调用异常: Unknown field for KeywordPlanHistoricalMetrics: suggested_bid_micros", "response_time": 1.12135648727417}, {"timestamp": "2025-07-17T13:07:27.283361", "seed_word": "Test", "success": false, "result_count": 0, "api_key_id": "api_2", "error_message": "API调用异常: Unknown field for KeywordPlanHistoricalMetrics: suggested_bid_micros", "response_time": 2.9799766540527344}, {"timestamp": "2025-07-17T13:07:28.135125", "seed_word": "Detection", "success": false, "result_count": 0, "api_key_id": "api_2", "error_message": "API调用异常: Unknown field for KeywordPlanHistoricalMetrics: suggested_bid_micros", "response_time": 0.8494312763214111}, {"timestamp": "2025-07-17T13:07:29.341669", "seed_word": "Home Testing", "success": false, "result_count": 0, "api_key_id": "api_2", "error_message": "API调用异常: Unknown field for KeywordPlanHistoricalMetrics: suggested_bid_micros", "response_time": 1.2040801048278809}, {"timestamp": "2025-07-17T13:07:30.696252", "seed_word": "Rapid Testing", "success": false, "result_count": 0, "api_key_id": "api_2", "error_message": "API调用异常: Unknown field for KeywordPlanHistoricalMetrics: suggested_bid_micros", "response_time": 1.3532516956329346}, {"timestamp": "2025-07-17T13:07:31.787540", "seed_word": "Allergy", "success": false, "result_count": 0, "api_key_id": "api_2", "error_message": "API调用异常: Unknown field for KeywordPlanHistoricalMetrics: suggested_bid_micros", "response_time": 1.0889008045196533}, {"timestamp": "2025-07-17T13:07:33.090257", "seed_word": "<PERSON>", "success": false, "result_count": 0, "api_key_id": "api_2", "error_message": "API调用异常: Unknown field for KeywordPlanHistoricalMetrics: suggested_bid_micros", "response_time": 1.300372838973999}, {"timestamp": "2025-07-17T13:07:35.392496", "seed_word": "Pet Allergy", "success": false, "result_count": 0, "api_key_id": "api_2", "error_message": "API调用异常: Unknown field for KeywordPlanHistoricalMetrics: suggested_bid_micros", "response_time": 2.3000502586364746}, {"timestamp": "2025-07-17T13:07:36.513862", "seed_word": "<PERSON><PERSON>", "success": false, "result_count": 0, "api_key_id": "api_2", "error_message": "API调用异常: Unknown field for KeywordPlanHistoricalMetrics: suggested_bid_micros", "response_time": 1.1189184188842773}, {"timestamp": "2025-07-17T13:07:37.835478", "seed_word": "Rapid Test", "success": false, "result_count": 0, "api_key_id": "api_2", "error_message": "API调用异常: Unknown field for KeywordPlanHistoricalMetrics: suggested_bid_micros", "response_time": 1.3191742897033691}, {"timestamp": "2025-07-17T13:07:39.343571", "seed_word": "Home Kit", "success": false, "result_count": 0, "api_key_id": "api_2", "error_message": "API调用异常: Unknown field for KeywordPlanHistoricalMetrics: suggested_bid_micros", "response_time": 1.5058343410491943}, {"timestamp": "2025-07-17T13:07:40.377749", "seed_word": "Test Kit", "success": false, "result_count": 0, "api_key_id": "api_2", "error_message": "API调用异常: Unknown field for KeywordPlanHistoricalMetrics: suggested_bid_micros", "response_time": 1.0320100784301758}, {"timestamp": "2025-07-17T13:07:42.221510", "seed_word": "Diagnostic Kit", "success": false, "result_count": 0, "api_key_id": "api_2", "error_message": "API调用异常: Unknown field for KeywordPlanHistoricalMetrics: suggested_bid_micros", "response_time": 1.8421595096588135}, {"timestamp": "2025-07-17T13:07:46.126153", "seed_word": "Dog", "success": false, "result_count": 0, "api_key_id": "api_2", "error_message": "API调用异常: Unknown field for KeywordPlanHistoricalMetrics: suggested_bid_micros", "response_time": 3.903064727783203}, {"timestamp": "2025-07-17T13:07:47.292359", "seed_word": "Allergen", "success": false, "result_count": 0, "api_key_id": "api_2", "error_message": "API调用异常: Unknown field for KeywordPlanHistoricalMetrics: suggested_bid_micros", "response_time": 1.1640207767486572}, {"timestamp": "2025-07-17T13:07:48.950688", "seed_word": "Pet", "success": false, "result_count": 0, "api_key_id": "api_2", "error_message": "API调用异常: Unknown field for KeywordPlanHistoricalMetrics: suggested_bid_micros", "response_time": 1.6569552421569824}, {"timestamp": "2025-07-17T13:07:49.890159", "seed_word": "Detection", "success": false, "result_count": 0, "api_key_id": "api_2", "error_message": "API调用异常: Unknown field for KeywordPlanHistoricalMetrics: suggested_bid_micros", "response_time": 0.9376747608184814}, {"timestamp": "2025-07-17T13:07:51.263649", "seed_word": "Rapid Testing", "success": false, "result_count": 0, "api_key_id": "api_2", "error_message": "API调用异常: Unknown field for KeywordPlanHistoricalMetrics: suggested_bid_micros", "response_time": 1.3709509372711182}, {"timestamp": "2025-07-17T13:07:52.489321", "seed_word": "Home Testing", "success": false, "result_count": 0, "api_key_id": "api_2", "error_message": "API调用异常: Unknown field for KeywordPlanHistoricalMetrics: suggested_bid_micros", "response_time": 1.2238762378692627}, {"timestamp": "2025-07-17T13:07:53.596412", "seed_word": "Screening", "success": false, "result_count": 0, "api_key_id": "api_2", "error_message": "API调用异常: Unknown field for KeywordPlanHistoricalMetrics: suggested_bid_micros", "response_time": 1.1046419143676758}, {"timestamp": "2025-07-17T13:07:54.654518", "seed_word": "Test", "success": false, "result_count": 0, "api_key_id": "api_2", "error_message": "API调用异常: Unknown field for KeywordPlanHistoricalMetrics: suggested_bid_micros", "response_time": 1.0555236339569092}, {"timestamp": "2025-07-17T13:07:55.835379", "seed_word": "Test", "success": false, "result_count": 0, "api_key_id": "api_2", "error_message": "API调用异常: Unknown field for KeywordPlanHistoricalMetrics: suggested_bid_micros", "response_time": 1.1782960891723633}, {"timestamp": "2025-07-17T13:07:56.964884", "seed_word": "Dermatophagoides <PERSON>", "success": false, "result_count": 0, "api_key_id": "api_2", "error_message": "API调用异常: Unknown field for KeywordPlanHistoricalMetrics: suggested_bid_micros", "response_time": 1.1268181800842285}, {"timestamp": "2025-07-17T13:07:58.388946", "seed_word": "Dust Mite Allergy", "success": false, "result_count": 0, "api_key_id": "api_2", "error_message": "API调用异常: Unknown field for KeywordPlanHistoricalMetrics: suggested_bid_micros", "response_time": 1.421724796295166}, {"timestamp": "2025-07-17T13:07:59.418410", "seed_word": "Allergy", "success": false, "result_count": 0, "api_key_id": "api_2", "error_message": "API调用异常: Unknown field for KeywordPlanHistoricalMetrics: suggested_bid_micros", "response_time": 1.0278041362762451}, {"timestamp": "2025-07-17T13:08:01.507142", "seed_word": "Triggers", "success": false, "result_count": 0, "api_key_id": "api_2", "error_message": "API调用异常: Unknown field for KeywordPlanHistoricalMetrics: suggested_bid_micros", "response_time": 2.0870347023010254}, {"timestamp": "2025-07-17T13:08:02.977368", "seed_word": "Rapid Test", "success": false, "result_count": 0, "api_key_id": "api_2", "error_message": "API调用异常: Unknown field for KeywordPlanHistoricalMetrics: suggested_bid_micros", "response_time": 1.4685766696929932}, {"timestamp": "2025-07-17T13:08:04.130762", "seed_word": "Home Test", "success": false, "result_count": 0, "api_key_id": "api_2", "error_message": "API调用异常: Unknown field for KeywordPlanHistoricalMetrics: suggested_bid_micros", "response_time": 1.1517653465270996}, {"timestamp": "2025-07-17T13:08:05.446236", "seed_word": "Test Kit", "success": false, "result_count": 0, "api_key_id": "api_2", "error_message": "API调用异常: Unknown field for KeywordPlanHistoricalMetrics: suggested_bid_micros", "response_time": 1.3135929107666016}, {"timestamp": "2025-07-17T13:08:06.453990", "seed_word": "Diagnostic Test", "success": false, "result_count": 0, "api_key_id": "api_2", "error_message": "API调用异常: Unknown field for KeywordPlanHistoricalMetrics: suggested_bid_micros", "response_time": 1.0047869682312012}, {"timestamp": "2025-07-17T13:08:07.818659", "seed_word": "Dust Mite", "success": false, "result_count": 0, "api_key_id": "api_2", "error_message": "API调用异常: Unknown field for KeywordPlanHistoricalMetrics: suggested_bid_micros", "response_time": 1.361612319946289}, {"timestamp": "2025-07-17T13:08:07.726117", "seed_word": "Allergen", "success": false, "result_count": 0, "api_key_id": "api_2", "error_message": "API调用异常: Unknown field for KeywordPlanHistoricalMetrics: suggested_bid_micros", "response_time": -0.09543514251708984}, {"timestamp": "2025-07-17T13:08:08.683395", "seed_word": "Test", "success": false, "result_count": 0, "api_key_id": "api_2", "error_message": "API调用异常: Unknown field for KeywordPlanHistoricalMetrics: suggested_bid_micros", "response_time": 0.9546902179718018}, {"timestamp": "2025-07-17T13:08:09.682426", "seed_word": "Detection", "success": false, "result_count": 0, "api_key_id": "api_2", "error_message": "API调用异常: Unknown field for KeywordPlanHistoricalMetrics: suggested_bid_micros", "response_time": 0.9969620704650879}, {"timestamp": "2025-07-17T13:08:10.662942", "seed_word": "Home Testing", "success": false, "result_count": 0, "api_key_id": "api_2", "error_message": "API调用异常: Unknown field for KeywordPlanHistoricalMetrics: suggested_bid_micros", "response_time": 0.97788405418396}, {"timestamp": "2025-07-17T13:08:11.722369", "seed_word": "Rapid Testing", "success": false, "result_count": 0, "api_key_id": "api_2", "error_message": "API调用异常: Unknown field for KeywordPlanHistoricalMetrics: suggested_bid_micros", "response_time": 1.0565719604492188}, {"timestamp": "2025-07-17T13:08:12.714974", "seed_word": "Egg White Allergy", "success": false, "result_count": 0, "api_key_id": "api_2", "error_message": "API调用异常: Unknown field for KeywordPlanHistoricalMetrics: suggested_bid_micros", "response_time": 0.9898030757904053}, {"timestamp": "2025-07-17T13:08:31.039195", "seed_word": "Egg Allergy", "success": false, "result_count": 0, "api_key_id": "api_2", "error_message": "API调用异常: 503 failed to connect to all addresses; last error: UNAVAILABLE: ipv4:************:7897: Socket closed", "response_time": 18.321385860443115}, {"timestamp": "2025-07-17T13:08:33.475994", "seed_word": "Intolerance", "success": false, "result_count": 0, "api_key_id": "api_2", "error_message": "API调用异常: Unknown field for KeywordPlanHistoricalMetrics: suggested_bid_micros", "response_time": 2.434237480163574}, {"timestamp": "2025-07-17T13:08:34.563532", "seed_word": "Food Allergy", "success": false, "result_count": 0, "api_key_id": "api_2", "error_message": "API调用异常: Unknown field for KeywordPlanHistoricalMetrics: suggested_bid_micros", "response_time": 1.08424711227417}, {"timestamp": "2025-07-17T13:08:35.856191", "seed_word": "Rapid Test", "success": false, "result_count": 0, "api_key_id": "api_2", "error_message": "API调用异常: Unknown field for KeywordPlanHistoricalMetrics: suggested_bid_micros", "response_time": 1.289491891860962}, {"timestamp": "2025-07-17T13:08:36.726625", "seed_word": "Home Kit", "success": false, "result_count": 0, "api_key_id": "api_2", "error_message": "API调用异常: Unknown field for KeywordPlanHistoricalMetrics: suggested_bid_micros", "response_time": 0.8674933910369873}, {"timestamp": "2025-07-17T13:08:37.684059", "seed_word": "Egg Allergy Test", "success": false, "result_count": 0, "api_key_id": "api_2", "error_message": "API调用异常: Unknown field for KeywordPlanHistoricalMetrics: suggested_bid_micros", "response_time": 0.9544734954833984}, {"timestamp": "2025-07-17T13:08:56.561185", "seed_word": "Diagnostic Kit", "success": false, "result_count": 0, "api_key_id": "api_2", "error_message": "API调用异常: 503 failed to connect to all addresses; last error: UNKNOWN: ipv4:************:7897: Handshak<PERSON> read failed", "response_time": 18.87400507926941}, {"timestamp": "2025-07-17T13:08:59.304183", "seed_word": "Egg White", "success": false, "result_count": 0, "api_key_id": "api_2", "error_message": "API调用异常: Unknown field for KeywordPlanHistoricalMetrics: suggested_bid_micros", "response_time": 2.740333318710327}, {"timestamp": "2025-07-17T13:09:02.069064", "seed_word": "Egg", "success": false, "result_count": 0, "api_key_id": "api_2", "error_message": "API调用异常: Unknown field for KeywordPlanHistoricalMetrics: suggested_bid_micros", "response_time": 2.761478900909424}, {"timestamp": "2025-07-17T13:09:03.287256", "seed_word": "Allergen", "success": false, "result_count": 0, "api_key_id": "api_2", "error_message": "API调用异常: Unknown field for KeywordPlanHistoricalMetrics: suggested_bid_micros", "response_time": 1.2151908874511719}, {"timestamp": "2025-07-17T13:09:11.421469", "seed_word": "Check", "success": false, "result_count": 0, "api_key_id": "api_2", "error_message": "API调用异常: Unknown field for KeywordPlanHistoricalMetrics: suggested_bid_micros", "response_time": 8.131154537200928}, {"timestamp": "2025-07-17T13:09:14.444113", "seed_word": "Detection", "success": false, "result_count": 0, "api_key_id": "api_2", "error_message": "API调用异常: Unknown field for KeywordPlanHistoricalMetrics: suggested_bid_micros", "response_time": 3.020840883255005}, {"timestamp": "2025-07-17T13:09:15.433950", "seed_word": "Home Testing", "success": false, "result_count": 0, "api_key_id": "api_2", "error_message": "API调用异常: Unknown field for KeywordPlanHistoricalMetrics: suggested_bid_micros", "response_time": 0.9868266582489014}, {"timestamp": "2025-07-17T13:09:19.436319", "seed_word": "Rapid Testing", "success": false, "result_count": 0, "api_key_id": "api_2", "error_message": "API调用异常: Unknown field for KeywordPlanHistoricalMetrics: suggested_bid_micros", "response_time": 3.9993393421173096}, {"timestamp": "2025-07-17T13:09:22.595044", "seed_word": "Mugwort Allergy", "success": false, "result_count": 0, "api_key_id": "api_2", "error_message": "API调用异常: Unknown field for KeywordPlanHistoricalMetrics: suggested_bid_micros", "response_time": 3.156670093536377}, {"timestamp": "2025-07-17T13:09:25.734390", "seed_word": "<PERSON><PERSON> Allergy", "success": false, "result_count": 0, "api_key_id": "api_2", "error_message": "API调用异常: Unknown field for KeywordPlanHistoricalMetrics: suggested_bid_micros", "response_time": 3.137005567550659}, {"timestamp": "2025-07-17T13:09:29.154943", "seed_word": "Hay Fever", "success": false, "result_count": 0, "api_key_id": "api_2", "error_message": "API调用异常: Unknown field for KeywordPlanHistoricalMetrics: suggested_bid_micros", "response_time": 3.4174747467041016}, {"timestamp": "2025-07-17T13:09:33.758489", "seed_word": "Symptoms", "success": false, "result_count": 0, "api_key_id": "api_2", "error_message": "API调用异常: Unknown field for KeywordPlanHistoricalMetrics: suggested_bid_micros", "response_time": 4.600491285324097}, {"timestamp": "2025-07-17T13:09:34.754150", "seed_word": "Triggers", "success": false, "result_count": 0, "api_key_id": "api_2", "error_message": "API调用异常: Unknown field for KeywordPlanHistoricalMetrics: suggested_bid_micros", "response_time": 0.9937138557434082}, {"timestamp": "2025-07-17T13:09:36.219152", "seed_word": "Rapid Test", "success": false, "result_count": 0, "api_key_id": "api_2", "error_message": "API调用异常: Unknown field for KeywordPlanHistoricalMetrics: suggested_bid_micros", "response_time": 1.4618990421295166}, {"timestamp": "2025-07-17T13:09:37.464464", "seed_word": "Home Test", "success": false, "result_count": 0, "api_key_id": "api_2", "error_message": "API调用异常: Unknown field for KeywordPlanHistoricalMetrics: suggested_bid_micros", "response_time": 1.2424616813659668}, {"timestamp": "2025-07-17T13:09:38.928268", "seed_word": "Test Kit", "success": false, "result_count": 0, "api_key_id": "api_2", "error_message": "API调用异常: Unknown field for KeywordPlanHistoricalMetrics: suggested_bid_micros", "response_time": 1.4616730213165283}, {"timestamp": "2025-07-17T13:09:39.909876", "seed_word": "Diagnostic Kit", "success": false, "result_count": 0, "api_key_id": "api_2", "error_message": "API调用异常: Unknown field for KeywordPlanHistoricalMetrics: suggested_bid_micros", "response_time": 0.9781084060668945}, {"timestamp": "2025-07-17T13:09:41.434454", "seed_word": "Mugwort", "success": false, "result_count": 0, "api_key_id": "api_2", "error_message": "API调用异常: Unknown field for KeywordPlanHistoricalMetrics: suggested_bid_micros", "response_time": 1.5224459171295166}, {"timestamp": "2025-07-17T13:09:43.229749", "seed_word": "<PERSON><PERSON>", "success": false, "result_count": 0, "api_key_id": "api_2", "error_message": "API调用异常: Unknown field for KeywordPlanHistoricalMetrics: suggested_bid_micros", "response_time": 1.7910089492797852}, {"timestamp": "2025-07-17T13:09:44.906085", "seed_word": "Allergen", "success": false, "result_count": 0, "api_key_id": "api_2", "error_message": "API调用异常: Unknown field for KeywordPlanHistoricalMetrics: suggested_bid_micros", "response_time": 1.672797441482544}, {"timestamp": "2025-07-17T13:09:46.007735", "seed_word": "Test", "success": false, "result_count": 0, "api_key_id": "api_2", "error_message": "API调用异常: Unknown field for KeywordPlanHistoricalMetrics: suggested_bid_micros", "response_time": 1.0983643531799316}, {"timestamp": "2025-07-17T13:09:46.865369", "seed_word": "Detection", "success": false, "result_count": 0, "api_key_id": "api_2", "error_message": "API调用异常: Unknown field for KeywordPlanHistoricalMetrics: suggested_bid_micros", "response_time": 0.8542642593383789}, {"timestamp": "2025-07-17T13:09:48.087016", "seed_word": "Home Testing", "success": false, "result_count": 0, "api_key_id": "api_2", "error_message": "API调用异常: Unknown field for KeywordPlanHistoricalMetrics: suggested_bid_micros", "response_time": 1.2181320190429688}, {"timestamp": "2025-07-17T13:09:49.390487", "seed_word": "Rapid Testing", "success": false, "result_count": 0, "api_key_id": "api_2", "error_message": "API调用异常: Unknown field for KeywordPlanHistoricalMetrics: suggested_bid_micros", "response_time": 1.3002574443817139}, {"timestamp": "2025-07-17T13:09:50.690451", "seed_word": "Allergy", "success": false, "result_count": 0, "api_key_id": "api_2", "error_message": "API调用异常: Unknown field for KeywordPlanHistoricalMetrics: suggested_bid_micros", "response_time": 1.2971949577331543}, {"timestamp": "2025-07-17T13:09:53.631308", "seed_word": "Peanut Allergy", "success": false, "result_count": 0, "api_key_id": "api_2", "error_message": "API调用异常: Unknown field for KeywordPlanHistoricalMetrics: suggested_bid_micros", "response_time": 2.9373090267181396}, {"timestamp": "2025-07-17T13:09:55.032842", "seed_word": "Food Allergy", "success": false, "result_count": 0, "api_key_id": "api_2", "error_message": "API调用异常: Unknown field for KeywordPlanHistoricalMetrics: suggested_bid_micros", "response_time": 1.397975206375122}, {"timestamp": "2025-07-17T13:09:56.076306", "seed_word": "Rapid Test", "success": false, "result_count": 0, "api_key_id": "api_2", "error_message": "API调用异常: Unknown field for KeywordPlanHistoricalMetrics: suggested_bid_micros", "response_time": 1.0415828227996826}, {"timestamp": "2025-07-17T13:09:57.200199", "seed_word": "Home Kit", "success": false, "result_count": 0, "api_key_id": "api_2", "error_message": "API调用异常: Unknown field for KeywordPlanHistoricalMetrics: suggested_bid_micros", "response_time": 1.121453046798706}, {"timestamp": "2025-07-17T13:09:58.329040", "seed_word": "Test Kit", "success": false, "result_count": 0, "api_key_id": "api_2", "error_message": "API调用异常: Unknown field for KeywordPlanHistoricalMetrics: suggested_bid_micros", "response_time": 1.1256539821624756}, {"timestamp": "2025-07-17T13:09:59.706600", "seed_word": "Diagnostic Kit", "success": false, "result_count": 0, "api_key_id": "api_2", "error_message": "API调用异常: Unknown field for KeywordPlanHistoricalMetrics: suggested_bid_micros", "response_time": 1.3752851486206055}, {"timestamp": "2025-07-17T13:10:02.352377", "seed_word": "Peanut", "success": false, "result_count": 0, "api_key_id": "api_2", "error_message": "API调用异常: Unknown field for KeywordPlanHistoricalMetrics: suggested_bid_micros", "response_time": 2.64323353767395}, {"timestamp": "2025-07-17T13:10:03.586547", "seed_word": "Allergen", "success": false, "result_count": 0, "api_key_id": "api_2", "error_message": "API调用异常: Unknown field for KeywordPlanHistoricalMetrics: suggested_bid_micros", "response_time": 1.2306277751922607}, {"timestamp": "2025-07-17T13:10:04.591283", "seed_word": "Detection", "success": false, "result_count": 0, "api_key_id": "api_2", "error_message": "API调用异常: Unknown field for KeywordPlanHistoricalMetrics: suggested_bid_micros", "response_time": 1.0008964538574219}, {"timestamp": "2025-07-17T13:10:06.082226", "seed_word": "Rapid Testing", "success": false, "result_count": 0, "api_key_id": "api_2", "error_message": "API调用异常: Unknown field for KeywordPlanHistoricalMetrics: suggested_bid_micros", "response_time": 1.4872486591339111}, {"timestamp": "2025-07-17T13:10:07.319394", "seed_word": "Home Testing", "success": false, "result_count": 0, "api_key_id": "api_2", "error_message": "API调用异常: Unknown field for KeywordPlanHistoricalMetrics: suggested_bid_micros", "response_time": 1.2335269451141357}, {"timestamp": "2025-07-17T13:10:08.278164", "seed_word": "Screening", "success": false, "result_count": 0, "api_key_id": "api_2", "error_message": "API调用异常: Unknown field for KeywordPlanHistoricalMetrics: suggested_bid_micros", "response_time": 0.9564228057861328}, {"timestamp": "2025-07-17T13:10:09.299784", "seed_word": "Test", "success": false, "result_count": 0, "api_key_id": "api_2", "error_message": "API调用异常: Unknown field for KeywordPlanHistoricalMetrics: suggested_bid_micros", "response_time": 1.0181055068969727}, {"timestamp": "2025-07-17T13:10:10.246111", "seed_word": "Test", "success": false, "result_count": 0, "api_key_id": "api_2", "error_message": "API调用异常: Unknown field for KeywordPlanHistoricalMetrics: suggested_bid_micros", "response_time": 0.9441299438476562}, {"timestamp": "2025-07-17T13:10:11.313625", "seed_word": "Test", "success": false, "result_count": 0, "api_key_id": "api_2", "error_message": "API调用异常: Unknown field for KeywordPlanHistoricalMetrics: suggested_bid_micros", "response_time": 1.0638067722320557}, {"timestamp": "2025-07-17T13:10:12.344095", "seed_word": "Test", "success": false, "result_count": 0, "api_key_id": "api_2", "error_message": "API调用异常: Unknown field for KeywordPlanHistoricalMetrics: suggested_bid_micros", "response_time": 1.026721477508545}, {"timestamp": "2025-07-17T13:10:12.104673", "seed_word": "Test", "success": false, "result_count": 0, "api_key_id": "api_2", "error_message": "API调用异常: Unknown field for KeywordPlanHistoricalMetrics: suggested_bid_micros", "response_time": -0.24341988563537598}, {"timestamp": "2025-07-17T13:10:13.129268", "seed_word": "Test", "success": false, "result_count": 0, "api_key_id": "api_2", "error_message": "API调用异常: Unknown field for KeywordPlanHistoricalMetrics: suggested_bid_micros", "response_time": 1.020819902420044}, {"timestamp": "2025-07-17T13:10:13.993914", "seed_word": "Test", "success": false, "result_count": 0, "api_key_id": "api_2", "error_message": "API调用异常: Unknown field for KeywordPlanHistoricalMetrics: suggested_bid_micros", "response_time": 0.8609914779663086}, {"timestamp": "2025-07-17T13:10:14.963587", "seed_word": "Test", "success": false, "result_count": 0, "api_key_id": "api_2", "error_message": "API调用异常: Unknown field for KeywordPlanHistoricalMetrics: suggested_bid_micros", "response_time": 0.965933084487915}, {"timestamp": "2025-07-17T13:10:15.890975", "seed_word": "Test", "success": false, "result_count": 0, "api_key_id": "api_2", "error_message": "API调用异常: Unknown field for KeywordPlanHistoricalMetrics: suggested_bid_micros", "response_time": 0.9236509799957275}, {"timestamp": "2025-07-17T13:10:16.808224", "seed_word": "Test", "success": false, "result_count": 0, "api_key_id": "api_2", "error_message": "API调用异常: Unknown field for KeywordPlanHistoricalMetrics: suggested_bid_micros", "response_time": 0.9144651889801025}, {"timestamp": "2025-07-17T13:10:17.692220", "seed_word": "Test", "success": false, "result_count": 0, "api_key_id": "api_2", "error_message": "API调用异常: Unknown field for KeywordPlanHistoricalMetrics: suggested_bid_micros", "response_time": 0.8799855709075928}, {"timestamp": "2025-07-17T13:10:18.603454", "seed_word": "Test", "success": false, "result_count": 0, "api_key_id": "api_2", "error_message": "API调用异常: Unknown field for KeywordPlanHistoricalMetrics: suggested_bid_micros", "response_time": 0.9072384834289551}, {"timestamp": "2025-07-17T13:10:19.496555", "seed_word": "Test", "success": false, "result_count": 0, "api_key_id": "api_2", "error_message": "API调用异常: Unknown field for KeywordPlanHistoricalMetrics: suggested_bid_micros", "response_time": 0.8895068168640137}, {"timestamp": "2025-07-17T13:10:20.506960", "seed_word": "<PERSON><PERSON>", "success": false, "result_count": 0, "api_key_id": "api_2", "error_message": "API调用异常: Unknown field for KeywordPlanHistoricalMetrics: suggested_bid_micros", "response_time": 1.0075819492340088}, {"timestamp": "2025-07-17T13:10:23.024368", "seed_word": "Allergies", "success": false, "result_count": 0, "api_key_id": "api_2", "error_message": "API调用异常: Unknown field for KeywordPlanHistoricalMetrics: suggested_bid_micros", "response_time": 2.51338529586792}, {"timestamp": "2025-07-17T13:10:26.063125", "seed_word": "Seasonal Allergy", "success": false, "result_count": 0, "api_key_id": "api_2", "error_message": "API调用异常: Unknown field for KeywordPlanHistoricalMetrics: suggested_bid_micros", "response_time": 3.0348029136657715}, {"timestamp": "2025-07-17T13:10:27.561419", "seed_word": "Grass Allergy", "success": false, "result_count": 0, "api_key_id": "api_2", "error_message": "API调用异常: Unknown field for KeywordPlanHistoricalMetrics: suggested_bid_micros", "response_time": 1.494204044342041}, {"timestamp": "2025-07-17T13:10:29.965606", "seed_word": "Tree Allergy", "success": false, "result_count": 0, "api_key_id": "api_2", "error_message": "API调用异常: Unknown field for KeywordPlanHistoricalMetrics: suggested_bid_micros", "response_time": 2.399991989135742}, {"timestamp": "2025-07-17T13:10:30.961670", "seed_word": "Rapid Test", "success": false, "result_count": 0, "api_key_id": "api_2", "error_message": "API调用异常: Unknown field for KeywordPlanHistoricalMetrics: suggested_bid_micros", "response_time": 0.9918887615203857}, {"timestamp": "2025-07-17T13:10:32.220522", "seed_word": "Combo Test", "success": false, "result_count": 0, "api_key_id": "api_2", "error_message": "API调用异常: Unknown field for KeywordPlanHistoricalMetrics: suggested_bid_micros", "response_time": 1.2546966075897217}, {"timestamp": "2025-07-17T13:10:33.507473", "seed_word": "Home Test", "success": false, "result_count": 0, "api_key_id": "api_2", "error_message": "API调用异常: Unknown field for KeywordPlanHistoricalMetrics: suggested_bid_micros", "response_time": 1.2826895713806152}, {"timestamp": "2025-07-17T13:10:34.561742", "seed_word": "Test Kit", "success": false, "result_count": 0, "api_key_id": "api_2", "error_message": "API调用异常: Unknown field for KeywordPlanHistoricalMetrics: suggested_bid_micros", "response_time": 1.0501012802124023}, {"timestamp": "2025-07-17T13:10:35.614351", "seed_word": "<PERSON><PERSON>", "success": false, "result_count": 0, "api_key_id": "api_2", "error_message": "API调用异常: Unknown field for KeywordPlanHistoricalMetrics: suggested_bid_micros", "response_time": 1.0494065284729004}, {"timestamp": "2025-07-17T13:10:38.926736", "seed_word": "Grass", "success": false, "result_count": 0, "api_key_id": "api_2", "error_message": "API调用异常: Unknown field for KeywordPlanHistoricalMetrics: suggested_bid_micros", "response_time": 3.3085544109344482}, {"timestamp": "2025-07-17T13:10:42.936524", "seed_word": "Tree", "success": false, "result_count": 0, "api_key_id": "api_2", "error_message": "API调用异常: Unknown field for KeywordPlanHistoricalMetrics: suggested_bid_micros", "response_time": 4.007527828216553}, {"timestamp": "2025-07-17T13:10:43.808618", "seed_word": "Weed", "success": true, "result_count": 0, "api_key_id": "api_2", "error_message": null, "response_time": 0.86928391456604}, {"timestamp": "2025-07-17T13:10:43.602212", "seed_word": "Allergen", "success": false, "result_count": 0, "api_key_id": "api_2", "error_message": "API调用异常: Unknown field for KeywordPlanHistoricalMetrics: suggested_bid_micros", "response_time": -0.20919442176818848}, {"timestamp": "2025-07-17T13:10:53.365600", "seed_word": "Test", "success": false, "result_count": 0, "api_key_id": "api_2", "error_message": "API调用异常: Unknown field for KeywordPlanHistoricalMetrics: suggested_bid_micros", "response_time": 9.759510040283203}, {"timestamp": "2025-07-17T13:10:55.927809", "seed_word": "Detect", "success": false, "result_count": 0, "api_key_id": "api_2", "error_message": "API调用异常: Unknown field for KeywordPlanHistoricalMetrics: suggested_bid_micros", "response_time": 2.55940580368042}, {"timestamp": "2025-07-17T13:11:01.282954", "seed_word": "Home Testing", "success": false, "result_count": 0, "api_key_id": "api_2", "error_message": "API调用异常: 503 failed to connect to all addresses; last error: UNAVAILABLE: ipv4:************:7897: Socket closed", "response_time": 5.350820302963257}, {"timestamp": "2025-07-17T13:11:06.555033", "seed_word": "Screening", "success": false, "result_count": 0, "api_key_id": "api_2", "error_message": "API调用异常: 503 failed to connect to all addresses; last error: UNAVAILABLE: ipv4:************:7897: Socket closed", "response_time": 5.2698798179626465}, {"timestamp": "2025-07-17T13:11:54.829331", "seed_word": "Allergy", "success": false, "result_count": 0, "api_key_id": "api_2", "error_message": "API调用异常: Unknown field for KeywordPlanHistoricalMetrics: suggested_bid_micros", "response_time": 48.27188301086426}, {"timestamp": "2025-07-17T13:11:56.290029", "seed_word": "Food Allergy", "success": false, "result_count": 0, "api_key_id": "api_2", "error_message": "API调用异常: Unknown field for KeywordPlanHistoricalMetrics: suggested_bid_micros", "response_time": 1.456418514251709}, {"timestamp": "2025-07-17T13:11:57.981544", "seed_word": "Results in Minutes", "success": false, "result_count": 0, "api_key_id": "api_2", "error_message": "API调用异常: Unknown field for KeywordPlanHistoricalMetrics: suggested_bid_micros", "response_time": 1.6872217655181885}, {"timestamp": "2025-07-17T13:11:59.018213", "seed_word": "Rapid Test", "success": false, "result_count": 0, "api_key_id": "api_2", "error_message": "API调用异常: Unknown field for KeywordPlanHistoricalMetrics: suggested_bid_micros", "response_time": 1.0323970317840576}, {"timestamp": "2025-07-17T13:12:00.028933", "seed_word": "Test Kit", "success": false, "result_count": 0, "api_key_id": "api_2", "error_message": "API调用异常: Unknown field for KeywordPlanHistoricalMetrics: suggested_bid_micros", "response_time": 1.006540298461914}, {"timestamp": "2025-07-17T13:12:01.217142", "seed_word": "Home Test", "success": false, "result_count": 0, "api_key_id": "api_2", "error_message": "API调用异常: Unknown field for KeywordPlanHistoricalMetrics: suggested_bid_micros", "response_time": 1.1853954792022705}, {"timestamp": "2025-07-17T13:12:03.816967", "seed_word": "Food Allergy Testing", "success": false, "result_count": 0, "api_key_id": "api_2", "error_message": "API调用异常: Unknown field for KeywordPlanHistoricalMetrics: suggested_bid_micros", "response_time": 2.5957939624786377}, {"timestamp": "2025-07-17T13:12:06.273443", "seed_word": "<PERSON>mp", "success": false, "result_count": 0, "api_key_id": "api_2", "error_message": "API调用异常: Unknown field for KeywordPlanHistoricalMetrics: suggested_bid_micros", "response_time": 2.4539732933044434}, {"timestamp": "2025-07-17T13:12:07.558854", "seed_word": "Allergen", "success": false, "result_count": 0, "api_key_id": "api_2", "error_message": "API调用异常: Unknown field for KeywordPlanHistoricalMetrics: suggested_bid_micros", "response_time": 1.2826502323150635}, {"timestamp": "2025-07-17T13:12:13.133206", "seed_word": "Food", "success": false, "result_count": 0, "api_key_id": "api_2", "error_message": "API调用异常: Unknown field for KeywordPlanHistoricalMetrics: suggested_bid_micros", "response_time": 5.570141792297363}, {"timestamp": "2025-07-17T13:12:15.483742", "seed_word": "Detect", "success": false, "result_count": 0, "api_key_id": "api_2", "error_message": "API调用异常: Unknown field for KeywordPlanHistoricalMetrics: suggested_bid_micros", "response_time": 2.3478212356567383}, {"timestamp": "2025-07-17T13:12:17.241176", "seed_word": "Testing", "success": false, "result_count": 0, "api_key_id": "api_2", "error_message": "API调用异常: Unknown field for KeywordPlanHistoricalMetrics: suggested_bid_micros", "response_time": 1.753108263015747}, {"timestamp": "2025-07-17T13:12:18.461487", "seed_word": "Home Testing", "success": false, "result_count": 0, "api_key_id": "api_2", "error_message": "API调用异常: Unknown field for KeywordPlanHistoricalMetrics: suggested_bid_micros", "response_time": 1.2175087928771973}, {"timestamp": "2025-07-17T13:12:19.565796", "seed_word": "Screening", "success": false, "result_count": 0, "api_key_id": "api_2", "error_message": "API调用异常: Unknown field for KeywordPlanHistoricalMetrics: suggested_bid_micros", "response_time": 1.099851369857788}, {"timestamp": "2025-07-17T13:12:20.796365", "seed_word": "Allergy", "success": false, "result_count": 0, "api_key_id": "api_2", "error_message": "API调用异常: Unknown field for KeywordPlanHistoricalMetrics: suggested_bid_micros", "response_time": 1.2260167598724365}, {"timestamp": "2025-07-17T13:12:22.254504", "seed_word": "Soy Allergy", "success": false, "result_count": 0, "api_key_id": "api_2", "error_message": "API调用异常: Unknown field for KeywordPlanHistoricalMetrics: suggested_bid_micros", "response_time": 1.4537925720214844}, {"timestamp": "2025-07-17T13:12:23.940471", "seed_word": "Food Allergy", "success": false, "result_count": 0, "api_key_id": "api_2", "error_message": "API调用异常: Unknown field for KeywordPlanHistoricalMetrics: suggested_bid_micros", "response_time": 1.6833293437957764}, {"timestamp": "2025-07-17T13:12:25.176328", "seed_word": "Intolerance", "success": false, "result_count": 0, "api_key_id": "api_2", "error_message": "API调用异常: Unknown field for KeywordPlanHistoricalMetrics: suggested_bid_micros", "response_time": 1.233217716217041}, {"timestamp": "2025-07-17T13:12:26.321383", "seed_word": "Rapid Test", "success": false, "result_count": 0, "api_key_id": "api_2", "error_message": "API调用异常: Unknown field for KeywordPlanHistoricalMetrics: suggested_bid_micros", "response_time": 1.142456293106079}, {"timestamp": "2025-07-17T13:12:27.605377", "seed_word": "Test Kit", "success": false, "result_count": 0, "api_key_id": "api_2", "error_message": "API调用异常: Unknown field for KeywordPlanHistoricalMetrics: suggested_bid_micros", "response_time": 1.2810852527618408}, {"timestamp": "2025-07-17T13:12:28.818810", "seed_word": "Home Test", "success": false, "result_count": 0, "api_key_id": "api_2", "error_message": "API调用异常: Unknown field for KeywordPlanHistoricalMetrics: suggested_bid_micros", "response_time": 1.2086901664733887}, {"timestamp": "2025-07-17T13:12:29.908995", "seed_word": "Soy Allergy Test", "success": false, "result_count": 0, "api_key_id": "api_2", "error_message": "API调用异常: Unknown field for KeywordPlanHistoricalMetrics: suggested_bid_micros", "response_time": 1.0853822231292725}, {"timestamp": "2025-07-17T13:12:32.385358", "seed_word": "Soybean", "success": false, "result_count": 0, "api_key_id": "api_2", "error_message": "API调用异常: Unknown field for KeywordPlanHistoricalMetrics: suggested_bid_micros", "response_time": 2.4713134765625}, {"timestamp": "2025-07-17T13:12:35.129122", "seed_word": "Soy", "success": false, "result_count": 0, "api_key_id": "api_2", "error_message": "API调用异常: Unknown field for KeywordPlanHistoricalMetrics: suggested_bid_micros", "response_time": 2.739391565322876}, {"timestamp": "2025-07-17T13:12:36.464297", "seed_word": "Allergen", "success": false, "result_count": 0, "api_key_id": "api_2", "error_message": "API调用异常: Unknown field for KeywordPlanHistoricalMetrics: suggested_bid_micros", "response_time": 1.3304214477539062}, {"timestamp": "2025-07-17T13:12:37.482328", "seed_word": "Detect", "success": false, "result_count": 0, "api_key_id": "api_2", "error_message": "API调用异常: Unknown field for KeywordPlanHistoricalMetrics: suggested_bid_micros", "response_time": 1.0132739543914795}, {"timestamp": "2025-07-17T13:12:39.539284", "seed_word": "Manage Diet", "success": false, "result_count": 0, "api_key_id": "api_2", "error_message": "API调用异常: Unknown field for KeywordPlanHistoricalMetrics: suggested_bid_micros", "response_time": 2.0523834228515625}, {"timestamp": "2025-07-17T13:12:40.582349", "seed_word": "Home Testing", "success": false, "result_count": 0, "api_key_id": "api_2", "error_message": "API调用异常: Unknown field for KeywordPlanHistoricalMetrics: suggested_bid_micros", "response_time": 1.0399844646453857}, {"timestamp": "2025-07-17T13:12:41.696134", "seed_word": "Screening", "success": false, "result_count": 0, "api_key_id": "api_2", "error_message": "API调用异常: Unknown field for KeywordPlanHistoricalMetrics: suggested_bid_micros", "response_time": 1.1089451313018799}, {"timestamp": "2025-07-17T13:12:42.666832", "seed_word": "<PERSON>", "success": false, "result_count": 0, "api_key_id": "api_2", "error_message": "API调用异常: Unknown field for KeywordPlanHistoricalMetrics: suggested_bid_micros", "response_time": 0.9679126739501953}, {"timestamp": "2025-07-17T13:12:43.636484", "seed_word": "Hay Fever", "success": false, "result_count": 0, "api_key_id": "api_2", "error_message": "API调用异常: Unknown field for KeywordPlanHistoricalMetrics: suggested_bid_micros", "response_time": 0.9657814502716064}, {"timestamp": "2025-07-17T13:12:44.732168", "seed_word": "Seasonal Allergy", "success": false, "result_count": 0, "api_key_id": "api_2", "error_message": "API调用异常: Unknown field for KeywordPlanHistoricalMetrics: suggested_bid_micros", "response_time": 1.091073751449585}, {"timestamp": "2025-07-17T13:12:45.697635", "seed_word": "Grass Allergy", "success": false, "result_count": 0, "api_key_id": "api_2", "error_message": "API调用异常: Unknown field for KeywordPlanHistoricalMetrics: suggested_bid_micros", "response_time": 0.9625012874603271}, {"timestamp": "2025-07-17T13:12:46.862755", "seed_word": "Rapid Test", "success": false, "result_count": 0, "api_key_id": "api_2", "error_message": "API调用异常: Unknown field for KeywordPlanHistoricalMetrics: suggested_bid_micros", "response_time": 1.160107135772705}, {"timestamp": "2025-07-17T13:12:48.028486", "seed_word": "Home Test", "success": false, "result_count": 0, "api_key_id": "api_2", "error_message": "API调用异常: Unknown field for KeywordPlanHistoricalMetrics: suggested_bid_micros", "response_time": 1.1609609127044678}, {"timestamp": "2025-07-17T13:12:49.875878", "seed_word": "Allergy Test", "success": false, "result_count": 0, "api_key_id": "api_2", "error_message": "API调用异常: Unknown field for KeywordPlanHistoricalMetrics: suggested_bid_micros", "response_time": 1.8444862365722656}, {"timestamp": "2025-07-17T13:12:51.085708", "seed_word": "Test Kit", "success": false, "result_count": 0, "api_key_id": "api_2", "error_message": "API调用异常: Unknown field for KeywordPlanHistoricalMetrics: suggested_bid_micros", "response_time": 1.2047398090362549}, {"timestamp": "2025-07-17T13:12:54.214353", "seed_word": "<PERSON>", "success": false, "result_count": 0, "api_key_id": "api_2", "error_message": "API调用异常: Unknown field for KeywordPlanHistoricalMetrics: suggested_bid_micros", "response_time": 3.123591184616089}, {"timestamp": "2025-07-17T13:12:57.970934", "seed_word": "Grass", "success": false, "result_count": 0, "api_key_id": "api_2", "error_message": "API调用异常: Unknown field for KeywordPlanHistoricalMetrics: suggested_bid_micros", "response_time": 3.751248359680176}, {"timestamp": "2025-07-17T13:12:59.087296", "seed_word": "Allergen", "success": false, "result_count": 0, "api_key_id": "api_2", "error_message": "API调用异常: Unknown field for KeywordPlanHistoricalMetrics: suggested_bid_micros", "response_time": 1.1110002994537354}, {"timestamp": "2025-07-17T13:13:00.081778", "seed_word": "Detect", "success": false, "result_count": 0, "api_key_id": "api_2", "error_message": "API调用异常: Unknown field for KeywordPlanHistoricalMetrics: suggested_bid_micros", "response_time": 0.9890859127044678}, {"timestamp": "2025-07-17T13:13:01.308274", "seed_word": "Home Testing", "success": false, "result_count": 0, "api_key_id": "api_2", "error_message": "API调用异常: Unknown field for KeywordPlanHistoricalMetrics: suggested_bid_micros", "response_time": 1.2213666439056396}, {"timestamp": "2025-07-17T13:13:02.449380", "seed_word": "Screening", "success": false, "result_count": 0, "api_key_id": "api_2", "error_message": "API调用异常: Unknown field for KeywordPlanHistoricalMetrics: suggested_bid_micros", "response_time": 1.1373951435089111}, {"timestamp": "2025-07-17T13:13:04.831257", "seed_word": "Diagnosis", "success": false, "result_count": 0, "api_key_id": "api_2", "error_message": "API调用异常: Unknown field for KeywordPlanHistoricalMetrics: suggested_bid_micros", "response_time": 2.376418113708496}, {"timestamp": "2025-07-17T13:13:07.176057", "seed_word": "IgE", "success": false, "result_count": 0, "api_key_id": "api_2", "error_message": "API调用异常: Unknown field for KeywordPlanHistoricalMetrics: suggested_bid_micros", "response_time": 2.33925199508667}, {"timestamp": "2025-07-17T13:13:08.300744", "seed_word": "Total IgE", "success": false, "result_count": 0, "api_key_id": "api_2", "error_message": "API调用异常: Unknown field for KeywordPlanHistoricalMetrics: suggested_bid_micros", "response_time": 1.1192150115966797}, {"timestamp": "2025-07-17T13:13:09.383079", "seed_word": "Allergies", "success": false, "result_count": 0, "api_key_id": "api_2", "error_message": "API调用异常: Unknown field for KeywordPlanHistoricalMetrics: suggested_bid_micros", "response_time": 1.0768229961395264}, {"timestamp": "2025-07-17T13:13:10.427130", "seed_word": "Allergy Screening", "success": false, "result_count": 0, "api_key_id": "api_2", "error_message": "API调用异常: Unknown field for KeywordPlanHistoricalMetrics: suggested_bid_micros", "response_time": 1.038522481918335}, {"timestamp": "2025-07-17T13:13:11.370514", "seed_word": "IgE Levels", "success": true, "result_count": 0, "api_key_id": "api_2", "error_message": null, "response_time": 0.9376978874206543}, {"timestamp": "2025-07-17T13:13:12.503104", "seed_word": "Rapid Test", "success": false, "result_count": 0, "api_key_id": "api_2", "error_message": "API调用异常: Unknown field for KeywordPlanHistoricalMetrics: suggested_bid_micros", "response_time": 1.1268837451934814}, {"timestamp": "2025-07-17T13:13:13.603993", "seed_word": "Test Kit", "success": false, "result_count": 0, "api_key_id": "api_2", "error_message": "API调用异常: Unknown field for KeywordPlanHistoricalMetrics: suggested_bid_micros", "response_time": 1.0955121517181396}, {"timestamp": "2025-07-17T13:13:14.600710", "seed_word": "Home Test", "success": false, "result_count": 0, "api_key_id": "api_2", "error_message": "API调用异常: Unknown field for KeywordPlanHistoricalMetrics: suggested_bid_micros", "response_time": 0.9930658340454102}, {"timestamp": "2025-07-17T13:13:16.914115", "seed_word": "Screening Test", "success": false, "result_count": 0, "api_key_id": "api_2", "error_message": "API调用异常: Unknown field for KeywordPlanHistoricalMetrics: suggested_bid_micros", "response_time": 2.3076488971710205}, {"timestamp": "2025-07-17T13:13:18.055755", "seed_word": "IgE", "success": false, "result_count": 0, "api_key_id": "api_2", "error_message": "API调用异常: Unknown field for KeywordPlanHistoricalMetrics: suggested_bid_micros", "response_time": 1.13584303855896}, {"timestamp": "2025-07-17T13:13:19.249552", "seed_word": "Antibody", "success": false, "result_count": 0, "api_key_id": "api_2", "error_message": "API调用异常: Unknown field for KeywordPlanHistoricalMetrics: suggested_bid_micros", "response_time": 1.1881301403045654}, {"timestamp": "2025-07-17T13:13:22.062124", "seed_word": "Screen", "success": false, "result_count": 0, "api_key_id": "api_2", "error_message": "API调用异常: Unknown field for KeywordPlanHistoricalMetrics: suggested_bid_micros", "response_time": 2.8068809509277344}, {"timestamp": "2025-07-17T13:13:24.580101", "seed_word": "Measure", "success": false, "result_count": 0, "api_key_id": "api_2", "error_message": "API调用异常: Unknown field for KeywordPlanHistoricalMetrics: suggested_bid_micros", "response_time": 2.5122194290161133}, {"timestamp": "2025-07-17T13:13:28.238383", "seed_word": "Monitor", "success": false, "result_count": 0, "api_key_id": "api_2", "error_message": "API调用异常: Unknown field for KeywordPlanHistoricalMetrics: suggested_bid_micros", "response_time": 3.6525728702545166}, {"timestamp": "2025-07-17T13:13:29.527597", "seed_word": "Home Testing", "success": false, "result_count": 0, "api_key_id": "api_2", "error_message": "API调用异常: Unknown field for KeywordPlanHistoricalMetrics: suggested_bid_micros", "response_time": 1.2843799591064453}, {"timestamp": "2025-07-17T13:13:30.629856", "seed_word": "Semen Parameter 10", "success": true, "result_count": 0, "api_key_id": "api_2", "error_message": null, "response_time": 1.0982887744903564}, {"timestamp": "2025-07-17T13:13:31.831224", "seed_word": "SP10", "success": false, "result_count": 0, "api_key_id": "api_2", "error_message": "API调用异常: Unknown field for KeywordPlanHistoricalMetrics: suggested_bid_micros", "response_time": 1.1954643726348877}, {"timestamp": "2025-07-17T13:13:32.759183", "seed_word": "Male Fertility", "success": true, "result_count": 0, "api_key_id": "api_2", "error_message": null, "response_time": 0.9223716259002686}, {"timestamp": "2025-07-17T13:13:33.666881", "seed_word": "Semen Analysis", "success": true, "result_count": 0, "api_key_id": "api_2", "error_message": null, "response_time": 0.9036669731140137}, {"timestamp": "2025-07-17T13:13:34.854279", "seed_word": "Sperm Parameters", "success": false, "result_count": 0, "api_key_id": "api_2", "error_message": "API调用异常: Unknown field for KeywordPlanHistoricalMetrics: suggested_bid_micros", "response_time": 1.1812961101531982}, {"timestamp": "2025-07-17T13:13:35.995663", "seed_word": "Rapid Test", "success": false, "result_count": 0, "api_key_id": "api_2", "error_message": "API调用异常: Unknown field for KeywordPlanHistoricalMetrics: suggested_bid_micros", "response_time": 1.1355385780334473}, {"timestamp": "2025-07-17T13:13:37.142297", "seed_word": "Test Kit", "success": false, "result_count": 0, "api_key_id": "api_2", "error_message": "API调用异常: Unknown field for KeywordPlanHistoricalMetrics: suggested_bid_micros", "response_time": 1.140894889831543}, {"timestamp": "2025-07-17T13:13:38.138080", "seed_word": "Test", "success": false, "result_count": 0, "api_key_id": "api_2", "error_message": "API调用异常: Unknown field for KeywordPlanHistoricalMetrics: suggested_bid_micros", "response_time": 0.9900326728820801}, {"timestamp": "2025-07-17T13:13:39.236270", "seed_word": "Home Test", "success": false, "result_count": 0, "api_key_id": "api_2", "error_message": "API调用异常: Unknown field for KeywordPlanHistoricalMetrics: suggested_bid_micros", "response_time": 1.0923082828521729}, {"timestamp": "2025-07-17T13:13:40.148305", "seed_word": "Semen", "success": true, "result_count": 0, "api_key_id": "api_2", "error_message": null, "response_time": 0.9062800407409668}, {"timestamp": "2025-07-17T13:13:42.249613", "seed_word": "Sperm", "success": false, "result_count": 0, "api_key_id": "api_2", "error_message": "API调用异常: Unknown field for KeywordPlanHistoricalMetrics: suggested_bid_micros", "response_time": 2.0955252647399902}, {"timestamp": "2025-07-17T13:13:44.398371", "seed_word": "<PERSON><PERSON><PERSON>", "success": false, "result_count": 0, "api_key_id": "api_2", "error_message": "API调用异常: Unknown field for KeywordPlanHistoricalMetrics: suggested_bid_micros", "response_time": 2.145346164703369}, {"timestamp": "2025-07-17T13:13:46.669305", "seed_word": "Analysis", "success": false, "result_count": 0, "api_key_id": "api_2", "error_message": "API调用异常: Unknown field for KeywordPlanHistoricalMetrics: suggested_bid_micros", "response_time": 2.2669765949249268}, {"timestamp": "2025-07-17T13:13:49.622158", "seed_word": "Check", "success": false, "result_count": 0, "api_key_id": "api_2", "error_message": "API调用异常: Unknown field for KeywordPlanHistoricalMetrics: suggested_bid_micros", "response_time": 2.9471964836120605}, {"timestamp": "2025-07-17T13:13:49.425928", "seed_word": "Home Testing", "success": false, "result_count": 0, "api_key_id": "api_2", "error_message": "API调用异常: Unknown field for KeywordPlanHistoricalMetrics: suggested_bid_micros", "response_time": -0.19999313354492188}, {"timestamp": "2025-07-17T13:13:50.557440", "seed_word": "Anaplasma", "success": false, "result_count": 0, "api_key_id": "api_2", "error_message": "API调用异常: Unknown field for KeywordPlanHistoricalMetrics: suggested_bid_micros", "response_time": 1.125556230545044}, {"timestamp": "2025-07-17T13:13:51.924387", "seed_word": "Antibody", "success": false, "result_count": 0, "api_key_id": "api_2", "error_message": "API调用异常: Unknown field for KeywordPlanHistoricalMetrics: suggested_bid_micros", "response_time": 1.360917091369629}, {"timestamp": "2025-07-17T13:13:54.162042", "seed_word": "Tick-borne Disease", "success": false, "result_count": 0, "api_key_id": "api_2", "error_message": "API调用异常: Unknown field for KeywordPlanHistoricalMetrics: suggested_bid_micros", "response_time": 2.2314555644989014}, {"timestamp": "2025-07-17T13:13:55.294472", "seed_word": "Diagnosis", "success": false, "result_count": 0, "api_key_id": "api_2", "error_message": "API调用异常: Unknown field for KeywordPlanHistoricalMetrics: suggested_bid_micros", "response_time": 1.1265864372253418}, {"timestamp": "2025-07-17T13:13:56.661206", "seed_word": "Rapid Test", "success": false, "result_count": 0, "api_key_id": "api_2", "error_message": "API调用异常: Unknown field for KeywordPlanHistoricalMetrics: suggested_bid_micros", "response_time": 1.3630127906799316}, {"timestamp": "2025-07-17T13:13:57.495439", "seed_word": "Antibody Test", "success": true, "result_count": 0, "api_key_id": "api_2", "error_message": null, "response_time": 0.8300216197967529}, {"timestamp": "2025-07-17T13:13:58.586194", "seed_word": "Test Kit", "success": false, "result_count": 0, "api_key_id": "api_2", "error_message": "API调用异常: Unknown field for KeywordPlanHistoricalMetrics: suggested_bid_micros", "response_time": 1.0845284461975098}, {"timestamp": "2025-07-17T13:13:59.557652", "seed_word": "Diagnostic Test", "success": false, "result_count": 0, "api_key_id": "api_2", "error_message": "API调用异常: Unknown field for KeywordPlanHistoricalMetrics: suggested_bid_micros", "response_time": 0.9652590751647949}, {"timestamp": "2025-07-17T13:14:00.460426", "seed_word": "Anaplasma", "success": false, "result_count": 0, "api_key_id": "api_2", "error_message": "API调用异常: Unknown field for KeywordPlanHistoricalMetrics: suggested_bid_micros", "response_time": 0.8967146873474121}, {"timestamp": "2025-07-17T13:14:02.814443", "seed_word": "Antibodies", "success": false, "result_count": 0, "api_key_id": "api_2", "error_message": "API调用异常: Unknown field for KeywordPlanHistoricalMetrics: suggested_bid_micros", "response_time": 2.3478755950927734}, {"timestamp": "2025-07-17T13:14:04.266568", "seed_word": "Whole Blood", "success": false, "result_count": 0, "api_key_id": "api_2", "error_message": "API调用异常: Unknown field for KeywordPlanHistoricalMetrics: suggested_bid_micros", "response_time": 1.4481658935546875}, {"timestamp": "2025-07-17T13:14:06.595724", "seed_word": "Serum", "success": false, "result_count": 0, "api_key_id": "api_2", "error_message": "API调用异常: Unknown field for KeywordPlanHistoricalMetrics: suggested_bid_micros", "response_time": 2.3249940872192383}, {"timestamp": "2025-07-17T13:14:08.774645", "seed_word": "Plasma", "success": false, "result_count": 0, "api_key_id": "api_2", "error_message": "API调用异常: Unknown field for KeywordPlanHistoricalMetrics: suggested_bid_micros", "response_time": 2.1725454330444336}, {"timestamp": "2025-07-17T13:14:09.658167", "seed_word": "Detect", "success": false, "result_count": 0, "api_key_id": "api_2", "error_message": "API调用异常: Unknown field for KeywordPlanHistoricalMetrics: suggested_bid_micros", "response_time": 0.8776121139526367}, {"timestamp": "2025-07-17T13:14:10.578145", "seed_word": "Diagnosis", "success": false, "result_count": 0, "api_key_id": "api_2", "error_message": "API调用异常: Unknown field for KeywordPlanHistoricalMetrics: suggested_bid_micros", "response_time": 0.9160950183868408}, {"timestamp": "2025-07-17T13:14:11.445022", "seed_word": "Testing", "success": false, "result_count": 0, "api_key_id": "api_2", "error_message": "API调用异常: Unknown field for KeywordPlanHistoricalMetrics: suggested_bid_micros", "response_time": 0.863091230392456}, {"timestamp": "2025-07-17T13:14:12.361231", "seed_word": "Screening", "success": false, "result_count": 0, "api_key_id": "api_2", "error_message": "API调用异常: Unknown field for KeywordPlanHistoricalMetrics: suggested_bid_micros", "response_time": 0.9099247455596924}, {"timestamp": "2025-07-17T13:14:13.497536", "seed_word": "<PERSON><PERSON>", "success": false, "result_count": 0, "api_key_id": "api_2", "error_message": "API调用异常: Unknown field for KeywordPlanHistoricalMetrics: suggested_bid_micros", "response_time": 1.1300599575042725}, {"timestamp": "2025-07-17T13:14:14.580819", "seed_word": "Antibody", "success": false, "result_count": 0, "api_key_id": "api_2", "error_message": "API调用异常: Unknown field for KeywordPlanHistoricalMetrics: suggested_bid_micros", "response_time": 1.076908826828003}, {"timestamp": "2025-07-17T13:14:15.899913", "seed_word": "<PERSON><PERSON><PERSON>", "success": false, "result_count": 0, "api_key_id": "api_2", "error_message": "API调用异常: Unknown field for KeywordPlanHistoricalMetrics: suggested_bid_micros", "response_time": 1.3125717639923096}, {"timestamp": "2025-07-17T13:14:16.866342", "seed_word": "Diagnosis", "success": false, "result_count": 0, "api_key_id": "api_2", "error_message": "API调用异常: Unknown field for KeywordPlanHistoricalMetrics: suggested_bid_micros", "response_time": 0.9603896141052246}, {"timestamp": "2025-07-17T13:14:18.099691", "seed_word": "Rapid Test", "success": false, "result_count": 0, "api_key_id": "api_2", "error_message": "API调用异常: Unknown field for KeywordPlanHistoricalMetrics: suggested_bid_micros", "response_time": 1.229773759841919}, {"timestamp": "2025-07-17T13:14:18.998098", "seed_word": "Antibody Test", "success": true, "result_count": 0, "api_key_id": "api_2", "error_message": null, "response_time": 0.8943159580230713}, {"timestamp": "2025-07-17T13:14:20.071898", "seed_word": "Test Kit", "success": false, "result_count": 0, "api_key_id": "api_2", "error_message": "API调用异常: Unknown field for KeywordPlanHistoricalMetrics: suggested_bid_micros", "response_time": 1.0671851634979248}, {"timestamp": "2025-07-17T13:14:20.053465", "seed_word": "Diagnostic Test", "success": false, "result_count": 0, "api_key_id": "api_2", "error_message": "API调用异常: Unknown field for KeywordPlanHistoricalMetrics: suggested_bid_micros", "response_time": -0.02482891082763672}, {"timestamp": "2025-07-17T13:14:21.014200", "seed_word": "<PERSON><PERSON>", "success": false, "result_count": 0, "api_key_id": "api_2", "error_message": "API调用异常: Unknown field for KeywordPlanHistoricalMetrics: suggested_bid_micros", "response_time": 0.9538090229034424}, {"timestamp": "2025-07-17T13:14:22.200378", "seed_word": "Antibodies", "success": false, "result_count": 0, "api_key_id": "api_2", "error_message": "API调用异常: Unknown field for KeywordPlanHistoricalMetrics: suggested_bid_micros", "response_time": 1.1796491146087646}, {"timestamp": "2025-07-17T13:14:24.906105", "seed_word": "Blood", "success": false, "result_count": 0, "api_key_id": "api_2", "error_message": "API调用异常: Unknown field for KeywordPlanHistoricalMetrics: suggested_bid_micros", "response_time": 2.6987833976745605}, {"timestamp": "2025-07-17T13:14:25.858182", "seed_word": "Serum", "success": false, "result_count": 0, "api_key_id": "api_2", "error_message": "API调用异常: Unknown field for KeywordPlanHistoricalMetrics: suggested_bid_micros", "response_time": 0.9454646110534668}, {"timestamp": "2025-07-17T13:14:26.780448", "seed_word": "Plasma", "success": false, "result_count": 0, "api_key_id": "api_2", "error_message": "API调用异常: Unknown field for KeywordPlanHistoricalMetrics: suggested_bid_micros", "response_time": 0.9156744480133057}, {"timestamp": "2025-07-17T13:14:27.885768", "seed_word": "Detect", "success": false, "result_count": 0, "api_key_id": "api_2", "error_message": "API调用异常: Unknown field for KeywordPlanHistoricalMetrics: suggested_bid_micros", "response_time": 1.0986747741699219}, {"timestamp": "2025-07-17T13:14:28.911562", "seed_word": "Diagnosis", "success": false, "result_count": 0, "api_key_id": "api_2", "error_message": "API调用异常: Unknown field for KeywordPlanHistoricalMetrics: suggested_bid_micros", "response_time": 1.019463300704956}, {"timestamp": "2025-07-17T13:14:29.962935", "seed_word": "Testing", "success": false, "result_count": 0, "api_key_id": "api_2", "error_message": "API调用异常: Unknown field for KeywordPlanHistoricalMetrics: suggested_bid_micros", "response_time": 1.0478837490081787}, {"timestamp": "2025-07-17T13:14:31.151820", "seed_word": "Screening", "success": false, "result_count": 0, "api_key_id": "api_2", "error_message": "API调用异常: Unknown field for KeywordPlanHistoricalMetrics: suggested_bid_micros", "response_time": 1.1848340034484863}, {"timestamp": "2025-07-17T13:14:32.446804", "seed_word": "Campylobacter", "success": false, "result_count": 0, "api_key_id": "api_2", "error_message": "API调用异常: Unknown field for KeywordPlanHistoricalMetrics: suggested_bid_micros", "response_time": 1.2875487804412842}, {"timestamp": "2025-07-17T13:14:34.988892", "seed_word": "Antigen", "success": false, "result_count": 0, "api_key_id": "api_2", "error_message": "API调用异常: Unknown field for KeywordPlanHistoricalMetrics: suggested_bid_micros", "response_time": 2.535548686981201}, {"timestamp": "2025-07-17T13:14:38.076818", "seed_word": "Food Poisoning", "success": false, "result_count": 0, "api_key_id": "api_2", "error_message": "API调用异常: Unknown field for KeywordPlanHistoricalMetrics: suggested_bid_micros", "response_time": 3.0791988372802734}, {"timestamp": "2025-07-17T13:14:39.778688", "seed_word": "Bacterial Gastroenteritis", "success": false, "result_count": 0, "api_key_id": "api_2", "error_message": "API调用异常: Unknown field for KeywordPlanHistoricalMetrics: suggested_bid_micros", "response_time": 1.6954820156097412}, {"timestamp": "2025-07-17T13:14:42.189480", "seed_word": "Bacteria", "success": false, "result_count": 0, "api_key_id": "api_2", "error_message": "API调用异常: Unknown field for KeywordPlanHistoricalMetrics: suggested_bid_micros", "response_time": 2.4066879749298096}, {"timestamp": "2025-07-17T13:14:43.252693", "seed_word": "Rapid Test", "success": false, "result_count": 0, "api_key_id": "api_2", "error_message": "API调用异常: Unknown field for KeywordPlanHistoricalMetrics: suggested_bid_micros", "response_time": 1.0567128658294678}, {"timestamp": "2025-07-17T13:14:46.072792", "seed_word": "Antigen Test", "success": false, "result_count": 0, "api_key_id": "api_2", "error_message": "API调用异常: Unknown field for KeywordPlanHistoricalMetrics: suggested_bid_micros", "response_time": 2.816272735595703}, {"timestamp": "2025-07-17T13:14:47.565479", "seed_word": "Stool Antigen Test", "success": false, "result_count": 0, "api_key_id": "api_2", "error_message": "API调用异常: Unknown field for KeywordPlanHistoricalMetrics: suggested_bid_micros", "response_time": 1.4885804653167725}, {"timestamp": "2025-07-17T13:14:48.695253", "seed_word": "Test Kit", "success": false, "result_count": 0, "api_key_id": "api_2", "error_message": "API调用异常: Unknown field for KeywordPlanHistoricalMetrics: suggested_bid_micros", "response_time": 1.1234381198883057}, {"timestamp": "2025-07-17T13:14:49.824038", "seed_word": "Campylobacter", "success": false, "result_count": 0, "api_key_id": "api_2", "error_message": "API调用异常: Unknown field for KeywordPlanHistoricalMetrics: suggested_bid_micros", "response_time": 1.1249780654907227}, {"timestamp": "2025-07-17T13:14:50.786792", "seed_word": "Antigen", "success": false, "result_count": 0, "api_key_id": "api_2", "error_message": "API调用异常: Unknown field for KeywordPlanHistoricalMetrics: suggested_bid_micros", "response_time": 0.9589226245880127}, {"timestamp": "2025-07-17T13:14:51.752638", "seed_word": "Stool", "success": false, "result_count": 0, "api_key_id": "api_2", "error_message": "API调用异常: Unknown field for KeywordPlanHistoricalMetrics: suggested_bid_micros", "response_time": 0.9622371196746826}, {"timestamp": "2025-07-17T13:14:52.688430", "seed_word": "Detect", "success": false, "result_count": 0, "api_key_id": "api_2", "error_message": "API调用异常: Unknown field for KeywordPlanHistoricalMetrics: suggested_bid_micros", "response_time": 0.9314126968383789}, {"timestamp": "2025-07-17T13:14:53.984372", "seed_word": "Diagnosis", "success": false, "result_count": 0, "api_key_id": "api_2", "error_message": "API调用异常: Unknown field for KeywordPlanHistoricalMetrics: suggested_bid_micros", "response_time": 1.2890534400939941}, {"timestamp": "2025-07-17T13:14:54.872676", "seed_word": "Testing", "success": false, "result_count": 0, "api_key_id": "api_2", "error_message": "API调用异常: Unknown field for KeywordPlanHistoricalMetrics: suggested_bid_micros", "response_time": 0.8812811374664307}, {"timestamp": "2025-07-17T13:14:55.815288", "seed_word": "Screening", "success": false, "result_count": 0, "api_key_id": "api_2", "error_message": "API调用异常: Unknown field for KeywordPlanHistoricalMetrics: suggested_bid_micros", "response_time": 0.9359142780303955}, {"timestamp": "2025-07-17T13:14:57.884843", "seed_word": "Clostridium difficile", "success": false, "result_count": 0, "api_key_id": "api_2", "error_message": "API调用异常: Unknown field for KeywordPlanHistoricalMetrics: suggested_bid_micros", "response_time": 2.0658864974975586}, {"timestamp": "2025-07-17T13:14:58.720173", "seed_word": "<PERSON><PERSON> diff", "success": true, "result_count": 0, "api_key_id": "api_2", "error_message": null, "response_time": 0.8313615322113037}, {"timestamp": "2025-07-17T13:14:59.721313", "seed_word": "Toxin A", "success": false, "result_count": 0, "api_key_id": "api_2", "error_message": "API调用异常: Unknown field for KeywordPlanHistoricalMetrics: suggested_bid_micros", "response_time": 0.9971718788146973}, {"timestamp": "2025-07-17T13:15:00.791608", "seed_word": "Toxin B", "success": false, "result_count": 0, "api_key_id": "api_2", "error_message": "API调用异常: Unknown field for KeywordPlanHistoricalMetrics: suggested_bid_micros", "response_time": 1.066406011581421}, {"timestamp": "2025-07-17T13:15:02.015358", "seed_word": "Glutamate Dehydrogenase", "success": false, "result_count": 0, "api_key_id": "api_2", "error_message": "API调用异常: Unknown field for KeywordPlanHistoricalMetrics: suggested_bid_micros", "response_time": 1.2197976112365723}, {"timestamp": "2025-07-17T13:15:03.098000", "seed_word": "Rapid Test", "success": false, "result_count": 0, "api_key_id": "api_2", "error_message": "API调用异常: Unknown field for KeywordPlanHistoricalMetrics: suggested_bid_micros", "response_time": 1.078805923461914}, {"timestamp": "2025-07-17T13:15:03.988403", "seed_word": "Combo Test", "success": false, "result_count": 0, "api_key_id": "api_2", "error_message": "API调用异常: Unknown field for KeywordPlanHistoricalMetrics: suggested_bid_micros", "response_time": 0.886350154876709}, {"timestamp": "2025-07-17T13:15:05.004163", "seed_word": "Antigen Test", "success": false, "result_count": 0, "api_key_id": "api_2", "error_message": "API调用异常: Unknown field for KeywordPlanHistoricalMetrics: suggested_bid_micros", "response_time": 1.011476755142212}, {"timestamp": "2025-07-17T13:15:06.087414", "seed_word": "Test Kit", "success": false, "result_count": 0, "api_key_id": "api_2", "error_message": "API调用异常: Unknown field for KeywordPlanHistoricalMetrics: suggested_bid_micros", "response_time": 1.0793395042419434}, {"timestamp": "2025-07-17T13:15:07.027901", "seed_word": "Clostridium difficile", "success": false, "result_count": 0, "api_key_id": "api_2", "error_message": "API调用异常: Unknown field for KeywordPlanHistoricalMetrics: suggested_bid_micros", "response_time": 0.9365813732147217}, {"timestamp": "2025-07-17T13:15:09.173260", "seed_word": "Toxin", "success": false, "result_count": 0, "api_key_id": "api_2", "error_message": "API调用异常: Unknown field for KeywordPlanHistoricalMetrics: suggested_bid_micros", "response_time": 2.14108943939209}, {"timestamp": "2025-07-17T13:15:10.208786", "seed_word": "GDH", "success": false, "result_count": 0, "api_key_id": "api_2", "error_message": "API调用异常: Unknown field for KeywordPlanHistoricalMetrics: suggested_bid_micros", "response_time": 1.0313193798065186}, {"timestamp": "2025-07-17T13:15:11.432301", "seed_word": "Antigen", "success": false, "result_count": 0, "api_key_id": "api_2", "error_message": "API调用异常: Unknown field for KeywordPlanHistoricalMetrics: suggested_bid_micros", "response_time": 1.2189574241638184}, {"timestamp": "2025-07-17T13:15:12.314598", "seed_word": "Detect", "success": false, "result_count": 0, "api_key_id": "api_2", "error_message": "API调用异常: Unknown field for KeywordPlanHistoricalMetrics: suggested_bid_micros", "response_time": 0.8779053688049316}, {"timestamp": "2025-07-17T13:15:13.275515", "seed_word": "Diagnosis", "success": false, "result_count": 0, "api_key_id": "api_2", "error_message": "API调用异常: Unknown field for KeywordPlanHistoricalMetrics: suggested_bid_micros", "response_time": 0.953765869140625}, {"timestamp": "2025-07-17T13:15:14.191678", "seed_word": "CDI Diagnosis", "success": false, "result_count": 0, "api_key_id": "api_2", "error_message": "API调用异常: Unknown field for KeywordPlanHistoricalMetrics: suggested_bid_micros", "response_time": 0.911475658416748}, {"timestamp": "2025-07-17T13:15:15.098144", "seed_word": "Testing", "success": false, "result_count": 0, "api_key_id": "api_2", "error_message": "API调用异常: Unknown field for KeywordPlanHistoricalMetrics: suggested_bid_micros", "response_time": 0.8990316390991211}, {"timestamp": "2025-07-17T13:15:16.114796", "seed_word": "Clostridium difficile", "success": false, "result_count": 0, "api_key_id": "api_2", "error_message": "API调用异常: Unknown field for KeywordPlanHistoricalMetrics: suggested_bid_micros", "response_time": 1.010406732559204}, {"timestamp": "2025-07-17T13:15:16.955333", "seed_word": "<PERSON><PERSON> diff", "success": true, "result_count": 0, "api_key_id": "api_2", "error_message": null, "response_time": 0.8361406326293945}, {"timestamp": "2025-07-17T13:15:17.845852", "seed_word": "GDH", "success": false, "result_count": 0, "api_key_id": "api_1", "error_message": "API调用异常: Unknown field for KeywordPlanHistoricalMetrics: suggested_bid_micros", "response_time": 0.8856124877929688}, {"timestamp": "2025-07-17T13:15:18.750728", "seed_word": "Toxin A", "success": false, "result_count": 0, "api_key_id": "api_1", "error_message": "API调用异常: Unknown field for KeywordPlanHistoricalMetrics: suggested_bid_micros", "response_time": 0.9008042812347412}, {"timestamp": "2025-07-17T13:15:19.630783", "seed_word": "Toxin B", "success": false, "result_count": 0, "api_key_id": "api_1", "error_message": "API调用异常: Unknown field for KeywordPlanHistoricalMetrics: suggested_bid_micros", "response_time": 0.8759021759033203}, {"timestamp": "2025-07-17T13:15:21.543593", "seed_word": "CDI", "success": false, "result_count": 0, "api_key_id": "api_1", "error_message": "API调用异常: Unknown field for KeywordPlanHistoricalMetrics: suggested_bid_micros", "response_time": 1.9089312553405762}, {"timestamp": "2025-07-17T13:15:22.581928", "seed_word": "Rapid Test", "success": false, "result_count": 0, "api_key_id": "api_1", "error_message": "API调用异常: Unknown field for KeywordPlanHistoricalMetrics: suggested_bid_micros", "response_time": 1.0338869094848633}, {"timestamp": "2025-07-17T13:15:22.223090", "seed_word": "Combo Test", "success": false, "result_count": 0, "api_key_id": "api_1", "error_message": "API调用异常: Unknown field for KeywordPlanHistoricalMetrics: suggested_bid_micros", "response_time": -0.3654305934906006}, {"timestamp": "2025-07-17T13:15:23.219955", "seed_word": "Antigen Test", "success": false, "result_count": 0, "api_key_id": "api_1", "error_message": "API调用异常: Unknown field for KeywordPlanHistoricalMetrics: suggested_bid_micros", "response_time": 0.9924697875976562}, {"timestamp": "2025-07-17T13:15:24.069529", "seed_word": "<PERSON><PERSON> Test", "success": true, "result_count": 0, "api_key_id": "api_1", "error_message": null, "response_time": 0.8448641300201416}, {"timestamp": "2025-07-17T13:15:24.991734", "seed_word": "Clostridium difficile", "success": false, "result_count": 0, "api_key_id": "api_1", "error_message": "API调用异常: Unknown field for KeywordPlanHistoricalMetrics: suggested_bid_micros", "response_time": 0.9146385192871094}, {"timestamp": "2025-07-17T13:15:25.822702", "seed_word": "Antigen", "success": false, "result_count": 0, "api_key_id": "api_1", "error_message": "API调用异常: Unknown field for KeywordPlanHistoricalMetrics: suggested_bid_micros", "response_time": 0.8238933086395264}, {"timestamp": "2025-07-17T13:26:24.237884", "seed_word": "2-Ethylidene-1", "success": false, "result_count": 0, "api_key_id": "api_1", "error_message": "API调用异常: Unknown field for KeywordPlanHistoricalMetrics: suggested_bid_micros", "response_time": 2.298401355743408}, {"timestamp": "2025-07-17T13:26:26.910787", "seed_word": "2C-B", "success": false, "result_count": 0, "api_key_id": "api_1", "error_message": "API调用异常: Unknown field for KeywordPlanHistoricalMetrics: suggested_bid_micros", "response_time": 1.6589562892913818}, {"timestamp": "2025-07-17T13:26:29.052240", "seed_word": "3-Methylmethcathinone", "success": false, "result_count": 0, "api_key_id": "api_1", "error_message": "API调用异常: Unknown field for KeywordPlanHistoricalMetrics: suggested_bid_micros", "response_time": 1.1351397037506104}, {"timestamp": "2025-07-17T13:26:31.392790", "seed_word": "3-diphenylpyrrolidine", "success": false, "result_count": 0, "api_key_id": "api_1", "error_message": "API调用异常: Unknown field for KeywordPlanHistoricalMetrics: suggested_bid_micros", "response_time": 1.331681728363037}, {"timestamp": "2025-07-17T13:26:33.994105", "seed_word": "3-in-1 Test", "success": false, "result_count": 0, "api_key_id": "api_1", "error_message": "API调用异常: Unknown field for KeywordPlanHistoricalMetrics: suggested_bid_micros", "response_time": 1.5959553718566895}, {"timestamp": "2025-07-17T13:26:36.185379", "seed_word": "4-Chloromethcathinone", "success": false, "result_count": 0, "api_key_id": "api_1", "error_message": "API调用异常: Unknown field for KeywordPlanHistoricalMetrics: suggested_bid_micros", "response_time": 1.1806516647338867}, {"timestamp": "2025-07-17T13:26:38.560425", "seed_word": "4-in-1 Test", "success": false, "result_count": 0, "api_key_id": "api_1", "error_message": "API调用异常: Unknown field for KeywordPlanHistoricalMetrics: suggested_bid_micros", "response_time": 1.3707852363586426}, {"timestamp": "2025-07-17T13:26:41.071627", "seed_word": "5-dimethyl-3", "success": true, "result_count": 1, "api_key_id": "api_1", "error_message": null, "response_time": 1.5068979263305664}, {"timestamp": "2025-07-17T13:26:43.540962", "seed_word": "6-MA<PERSON>", "success": false, "result_count": 0, "api_key_id": "api_1", "error_message": "API调用异常: Unknown field for KeywordPlanHistoricalMetrics: suggested_bid_micros", "response_time": 1.4602904319763184}, {"timestamp": "2025-07-17T13:26:45.589070", "seed_word": "6-Monoacetylmorphine", "success": false, "result_count": 0, "api_key_id": "api_1", "error_message": "API调用异常: Unknown field for KeywordPlanHistoricalMetrics: suggested_bid_micros", "response_time": 1.0269560813903809}, {"timestamp": "2025-07-17T13:26:46.384495", "seed_word": "7-Aminoclonazepam", "success": false, "result_count": 0, "api_key_id": "api_1", "error_message": "API调用异常: Unknown field for KeywordPlanHistoricalMetrics: suggested_bid_micros", "response_time": 1.0400443077087402}, {"timestamp": "2025-07-17T13:26:48.317788", "seed_word": "7-aminoclonazepam", "success": false, "result_count": 0, "api_key_id": "api_1", "error_message": "API调用异常: Unknown field for KeywordPlanHistoricalMetrics: suggested_bid_micros", "response_time": 0.9216597080230713}, {"timestamp": "2025-07-17T13:26:50.292334", "seed_word": "AB-PINACA", "success": false, "result_count": 0, "api_key_id": "api_1", "error_message": "API调用异常: Unknown field for KeywordPlanHistoricalMetrics: suggested_bid_micros", "response_time": 0.9513344764709473}, {"timestamp": "2025-07-17T13:26:53.869544", "seed_word": "ACR", "success": false, "result_count": 0, "api_key_id": "api_1", "error_message": "API调用异常: Unknown field for KeywordPlanHistoricalMetrics: suggested_bid_micros", "response_time": 2.5695767402648926}, {"timestamp": "2025-07-17T13:26:56.207695", "seed_word": "ADENO", "success": false, "result_count": 0, "api_key_id": "api_1", "error_message": "API调用异常: Unknown field for KeywordPlanHistoricalMetrics: suggested_bid_micros", "response_time": 1.3276100158691406}, {"timestamp": "2025-07-17T13:27:00.749676", "seed_word": "ADHD Medication", "success": false, "result_count": 0, "api_key_id": "api_1", "error_message": "API调用异常: Unknown field for KeywordPlanHistoricalMetrics: suggested_bid_micros", "response_time": 3.5360796451568604}, {"timestamp": "2025-07-17T13:27:03.604372", "seed_word": "ADV", "success": false, "result_count": 0, "api_key_id": "api_1", "error_message": "API调用异常: Unknown field for KeywordPlanHistoricalMetrics: suggested_bid_micros", "response_time": 1.8459906578063965}, {"timestamp": "2025-07-17T13:27:06.030561", "seed_word": "ADV Ag", "success": false, "result_count": 0, "api_key_id": "api_1", "error_message": "API调用异常: Unknown field for KeywordPlanHistoricalMetrics: suggested_bid_micros", "response_time": 1.4102592468261719}, {"timestamp": "2025-07-17T13:27:09.027145", "seed_word": "AFP", "success": false, "result_count": 0, "api_key_id": "api_1", "error_message": "API调用异常: Unknown field for KeywordPlanHistoricalMetrics: suggested_bid_micros", "response_time": 1.9833416938781738}, {"timestamp": "2025-07-17T13:27:11.003123", "seed_word": "AMH", "success": true, "result_count": 0, "api_key_id": "api_1", "error_message": null, "response_time": 0.9659140110015869}, {"timestamp": "2025-07-17T13:27:16.616283", "seed_word": "AMP", "success": false, "result_count": 0, "api_key_id": "api_1", "error_message": "API调用异常: Unknown field for KeywordPlanHistoricalMetrics: suggested_bid_micros", "response_time": 2.5932414531707764}, {"timestamp": "2025-07-17T13:27:17.360689", "seed_word": "APVP", "success": true, "result_count": 0, "api_key_id": "api_1", "error_message": null, "response_time": 1.0313045978546143}, {"timestamp": "2025-07-17T13:28:26.523534", "seed_word": "ELISA", "success": true, "result_count": 449, "api_key_id": "api_1", "error_message": null, "response_time": 3.0498108863830566}, {"timestamp": "2025-07-17T13:28:27.740448", "seed_word": "Test Kit", "success": true, "result_count": 746, "api_key_id": "api_1", "error_message": null, "response_time": 1.2092678546905518}, {"timestamp": "2025-07-17T13:28:28.889059", "seed_word": "Rapid Test", "success": true, "result_count": 687, "api_key_id": "api_1", "error_message": null, "response_time": 1.1390419006347656}, {"timestamp": "2025-07-17T13:28:52.430213", "seed_word": "test", "success": true, "result_count": 117, "api_key_id": "api_1", "error_message": null, "response_time": 3.3649299144744873}, {"timestamp": "2025-07-17T13:29:12.316165", "seed_word": "2-Ethylidene-1", "success": true, "result_count": 2, "api_key_id": "api_1", "error_message": null, "response_time": 1.0616369247436523}, {"timestamp": "2025-07-17T13:29:14.689807", "seed_word": "2C-B", "success": true, "result_count": 1, "api_key_id": "api_1", "error_message": null, "response_time": 1.3589904308319092}]