[{"timestamp": "2025-07-17T11:42:16.130579", "seed_word": "ELISA", "success": false, "result_count": 0, "api_key_id": "api_1", "error_message": "API调用异常: Specified service LanguageConstantService\" does not exist in Google Ads API v20.", "response_time": 0.4530973434448242}, {"timestamp": "2025-07-17T11:47:16.766996", "seed_word": "ELISA", "success": false, "result_count": 0, "api_key_id": "api_1", "error_message": "API调用异常: Unknown field for GenerateKeywordIdeasRequest: language_constants", "response_time": 0.43679094314575195}, {"timestamp": "2025-07-17T11:50:24.543264", "seed_word": "ELISA", "success": false, "result_count": 0, "api_key_id": "api_1", "error_message": "API调用异常: 'str' object has no attribute 'append'", "response_time": 0.44921875}, {"timestamp": "2025-07-17T11:50:56.291474", "seed_word": "ELISA", "success": false, "result_count": 0, "api_key_id": "api_1", "error_message": "API调用异常: Unknown field for KeywordPlanHistoricalMetrics: suggested_bid_micros", "response_time": 3.4778385162353516}, {"timestamp": "2025-07-17T12:02:41.046912", "seed_word": "test_keyword_0", "success": true, "result_count": 5, "api_key_id": "api_1", "error_message": null, "response_time": null}, {"timestamp": "2025-07-17T12:02:41.047146", "seed_word": "test_keyword_1", "success": true, "result_count": 5, "api_key_id": "api_1", "error_message": null, "response_time": null}, {"timestamp": "2025-07-17T12:02:41.047466", "seed_word": "test_keyword_2", "success": true, "result_count": 5, "api_key_id": "api_1", "error_message": null, "response_time": null}, {"timestamp": "2025-07-17T12:02:41.047699", "seed_word": "test_keyword_3", "success": true, "result_count": 5, "api_key_id": "api_1", "error_message": null, "response_time": null}, {"timestamp": "2025-07-17T12:02:41.047898", "seed_word": "test_keyword_4", "success": true, "result_count": 5, "api_key_id": "api_1", "error_message": null, "response_time": null}, {"timestamp": "2025-07-17T12:02:41.048135", "seed_word": "test_keyword_5", "success": true, "result_count": 5, "api_key_id": "api_1", "error_message": null, "response_time": null}, {"timestamp": "2025-07-17T12:02:41.048325", "seed_word": "test_keyword_6", "success": true, "result_count": 5, "api_key_id": "api_1", "error_message": null, "response_time": null}, {"timestamp": "2025-07-17T12:02:41.048565", "seed_word": "test_keyword_7", "success": true, "result_count": 5, "api_key_id": "api_1", "error_message": null, "response_time": null}, {"timestamp": "2025-07-17T12:02:41.048882", "seed_word": "test_keyword_8", "success": true, "result_count": 5, "api_key_id": "api_1", "error_message": null, "response_time": null}, {"timestamp": "2025-07-17T12:02:41.049168", "seed_word": "test_keyword_9", "success": true, "result_count": 5, "api_key_id": "api_1", "error_message": null, "response_time": null}]